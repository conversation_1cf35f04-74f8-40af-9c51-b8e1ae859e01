version: 2.1

jobs:
    build:
        machine:
            image: ubuntu-2204:2024.05.1
        resource_class: medium
        steps:
            - checkout
            - run:
                  name: 检查 VERSION 文件是否有变化
                  command: |
                      if git diff --quiet HEAD^ HEAD -- VERSION; then
                        echo "VERSION 文件没有变化。跳过构建。"
                        circleci-agent step halt
                      else
                        echo "VERSION 文件有变化。继续构建。"
                      fi
            - run:
                  name: 登录到 UCloud Docker 仓库
                  command: |
                      echo "$UCLOUD_DOCKER_PASSWORD" | docker login uhub.service.ucloud.cn -u "$UCLOUD_DOCKER_USERNAME" --password-stdin
            - run:
                  name: 构建和推送 Docker 镜像
                  command: |
                      make build
                      make push

workflows:
    version: 2
    check-and-build-workflow:
        jobs:
            - build:
                  filters:
                      branches:
                          only: main
