// @ts-check
import { defineConfig } from "astro/config";
import starlight from "@astrojs/starlight";
import tailwind from "@astrojs/tailwind";
import partytown from "@astrojs/partytown";
import node from "@astrojs/node";
import sitemap from "@astrojs/sitemap";

// https://astro.build/config
export default defineConfig({
	prefetch: false,
	site: "https://flowai.cc",
	server: {
		port: 4322,
	},
	integrations: [
		tailwind(),
		starlight({
			title: "FlowAI 文档",
			logo: {
				alt: "FlowAI Logo",
				src: "./public/main-logo-no-bg.png",
				replacesTitle: true,
			},
			favicon: "/favicon.ico",
			customCss: ["./src/content.css"],
			head: [
				{
					tag: "script",
					attrs: {
						src: "https://www.googletagmanager.com/gtag/js?id=G-ZHKDPMY03J",
					},
				},
				{
					tag: "script",
					content: `
					window.dataLayer = window.dataLayer || [];
					function gtag() {
						dataLayer.push(arguments);
					}
					gtag("js", new Date());
					gtag("config", "G-ZHKDPMY03J");
					`,
				},
				{
					tag: "script",
					content: `
					(function(c,l,a,r,i,t,y){
						c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
						t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
						y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
					})(window, document, "clarity", "script", "plt8z3yibx");
					`,
				},
				{
					tag: "script",
					content: `var _hmt = _hmt || [];
					(function() {
					var hm = document.createElement("script");
					hm.src = "https://hm.baidu.com/hm.js?976b5c60ea7e17a453980f639a6ac644";
					var s = document.getElementsByTagName("script")[0]; 
					s.parentNode.insertBefore(hm, s);
					})();
					`,
				},
			],
			sidebar: [
				{
					label: "介绍",
					translations: {
						en: "Introduction",
					},
					autogenerate: { directory: "intro" },
				},
				{
					label: "使用教程",
					translations: {
						en: "Tutorial",
					},
					autogenerate: { directory: "tutorial" },
				},
				// {
				// 	label: "最佳实践",
				// 	autogenerate: { directory: "best-practice" },
				// },
			],
			defaultLocale: "root",
			locales: {
				root: {
					label: "中文",
					lang: "zh",
				},
				en: {
					label: "English",
					lang: "en",
				},
			},
		}),
		partytown(),
		sitemap({
			customPages: ["https://flowai.cc/", "https://flowai.cc/privacy_policy"],
		}),
	],

	output: "server",

	adapter: node({
		mode: "standalone",
	}),
});
