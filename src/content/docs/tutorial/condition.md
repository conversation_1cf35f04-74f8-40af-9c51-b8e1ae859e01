---
title: 条件判断节点
description: 深入解析FlowAI条件判断节点的使用方法，掌握字符串匹配、分支控制等核心功能。通过实际案例学习如何实现用户分类、消息处理、文件匹配等常见场景。
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI条件判断, 工作流分支控制, 字符串匹配, 流程控制节点, 条件判断最佳实践, 工作流调试技巧"
    - tag: title
      content: "FlowAI 条件判断节点 - 工作流控制指南 | FlowAI 文档"
---

条件判断节点是FlowAI中的核心控制组件，它能根据设定的条件将工作流程分成不同的执行路径。通过合理使用条件判断，你可以构建出复杂的业务逻辑。

:::note
重要说明：条件判断节点将所有输入内容都视为字符串进行处理。这意味着即使输入的是数字,也会被转换为字符串进行比较。因此不支持数值大小比较操作（如大于、小于等）。
:::

## 节点配置

### 基础设置

![条件判断节点基础设置](./condition/base-setting.png)

1. **节点名称**

    - 为条件判断节点设置一个描述性名称
    - 建议使用能体现判断目的的名称，如"检查年龄"、"验证权限"等
    - 命名规范：使用动词+名词形式，如"验证用户权限"、"检查订单状态"

2. **判断内容**

    - 没有仅支持变量引用，需要保证当前节点之前有变量输出

3. **条件选项**
   支持以下字符串比较方式：
    - **等于/不等于**：精确匹配字符串
    - **包含/不包含**：检查是否包含子字符串，也就是说只要包含指定内容就算匹配成功
    - **开头等于/不等于**：检查字符串前缀
    - **结尾等于/不等于**：检查字符串后缀

## 使用示例

### 1. 用户类型判断

让我们创建一个简单的用户类型判断流程，这是FlowAI中最常用的场景之一：

1. **输入节点配置**

    ![用户类型输入节点配置](./condition/user-type-input-node.png)

2. **条件判断配置**

    ```
    节点名称：用户类型检查
    判断内容：$输入.userType
    条件1：等于 "VIP"
    条件2：等于 "普通用户"
    条件3：包含 "" // 用包含空字符串来表示其他情况，可以匹配到所有未匹配的情况
    ```

    _**注意**：条件匹配是按顺序匹配的，所以条件3需要放在最后，否则会匹配到其他情况！_

    ![条件判断配置](./condition/user-type-condition-node.png)

3. **输出节点配置**
   用不同分支来处理不同的用户类型
   ![输出节点配置](./condition/user-type-output-node.png)

### 2. 消息类型分类

一个实用的例子 - 根据消息内容进行不同的响应处理，适用于聊天机器人、客服系统等场景：

1. **输入节点配置**
   ![消息输入节点配置](./condition/message-input-node.png)

2. **条件判断配置**

    ```
    判断内容：$输入.message
    条件1：开头等于 "/help"  // 帮助命令
    条件2：开头等于 "/search"  // 搜索命令
    条件3：包含 "订单号:"  // 订单查询
    条件4：包含 空字符串  // 普通消息
    ```

    ![条件判断配置](./condition/message-condition-node.png)

3. **处理节点配置（每个分支）**

    比如你可以：

    - 帮助命令分支：返回帮助信息列表
    - 搜索命令分支：执行搜索操作
    - 订单查询分支：查询订单状态
    - 普通消息分支：返回默认回复

### 3. 文件名匹配示例

展示如何使用字符串相关的判断条件，适用于文件处理、自动化工作流等场景：

```
节点名称：文件分类
判断内容：$输入.filename
条件1：结尾等于 ".jpg" 或 结尾等于 ".png" （图片文件）
条件2：结尾等于 ".pdf" （PDF文档）
条件3：结尾等于 ".zip" （压缩包）
条件4：其他情况 （未知类型）
```

![文件类型条件判断配置](./condition/file-condition-node.png)

## 高级用法

### 1. 嵌套条件

你可以通过连接多个条件判断节点来实现复杂的逻辑，适用于多级审批、复杂业务规则等场景：

```
[年龄检查] --> [身份检查] --> [权限检查]
```

### 2. 组合条件示例

在一个判断节点中使用多个条件，适用于需要同时满足多个条件的场景：

```
条件1：等于 "premium" 且 包含 "plus"  // 匹配 "premium plus"
条件2：开头等于 "basic"  // 匹配 "basic"开头的所有内容
条件3：其他情况
```

## 最佳实践

1. **条件设计原则**

    - 注意所有比较都是基于字符串的
    - 条件之间应该互斥
    - 条件应该完整覆盖所有可能情况
    - 优先处理特殊情况
    - 使用注释说明每个条件的业务含义
    - 保持条件逻辑简单可维护

2. **变量处理**

    - 在条件判断前先确保变量存在
    - 考虑数据类型的一致性
    - 注意大小写敏感性

3. **错误处理**
    - 添加默认分支处理异常情况
    - 在关键分支添加日志记录
    - 考虑数据验证的必要性

## 常见问题

:::caution
常见错误：

1. **条件顺序问题**

    ```
    判断内容：$输入.message
    条件1：包含 "你好"
    条件2：等于 "你好世界"  // ❌ 错误：永远不会匹配到，因为是按照顺序匹配的
    ```

    正确做法：

    ```
    判断内容：$输入.message
    条件1：等于 "你好世界"  // ✅ 正确：先判断更具体的条件
    条件2：包含 "你好"
    ```

2. **遗漏情况**

    ```
    条件1：等于 "成功"
    条件2：等于 "失败"
    // 遗漏了其他可能的状态
    ```

    正确做法：添加默认分支处理其他情况

3. **大小写敏感**

    ```
    判断内容：$输入.status
    条件1：等于 "Success"  // 不会匹配 "success"
    ```

    解决方案：注意字符串大小写的精确匹配

4. **性能优化**
    - 避免过多嵌套条件

:::

## 调试技巧

**使用输出节点**
在每个分支添加输出节点，帮助调试流程：

```
[条件判断] --> [输出节点(打印判断结果)]
```

通过合理使用条件判断节点，你可以构建出灵活而强大的工作流程。记住要仔细规划判断逻辑，确保条件的完整性和互斥性，这样才能保证工作流程的可靠运行。
