---
title: LLM意图分类节点
description: 全面解析FlowAI中LLM意图分类节点的使用方法，包括配置指南、使用示例、最佳实践和调试技巧。学习如何利用大语言模型实现智能内容分类、情感分析和意图识别。
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI意图分类, 大语言模型, 智能内容分类, 情感分析, 意图识别, 工作流控制, 调试技巧"
    - tag: title
      content: "FlowAI 意图分类节点 - 智能内容分类与意图识别教程 | FlowAI 文档"
---

LLM意图分类节点是FlowAI平台的核心功能之一，它结合了大语言模型（LLM）的智能理解能力和条件判断的分支控制功能。通过这个节点，您可以实现：

- 智能内容分类
- 情感倾向分析
- 用户意图识别
- 多维度内容审核

## 节点配置指南

### 基础设置

![基础设置](./llm-intent/base-setting.png)

1. **节点命名规范**

    - 使用描述性名称
    - 示例：内容审核、情感分析、用户意图识别

2. **模型选择建议**

    - 推荐使用GPT-4o等高性能模型
    - 根据业务需求选择合适模型版本
    - 也可以从成本的角度选择其他模型，比如性价比和性能都不错的DeepSeek

3. **分类类别设计**

    - 类别数量：2-5个
    - 确保类别互斥且全面
    - 示例：正面/负面/中性

4. **提示词优化技巧**
    - 包含明确分类标准
    - 使用结构化指令
    - 提供示例参考

## 使用示例

### 1. 内容友好度检测

一个检测用户输入内容是否友好的示例：

1. **输入节点配置**

    ```
    节点名称：开始
    变量名称：content
    类型：文本输入
    ```

    ![开始节点](./llm-intent/user-input.png)

2. **LLM意图分类配置**

    ```
    节点名称：友好度检测
    模型：GPT-4o
    分类提示词：你是一个专业的内容审核员，需要判断用户输入的内容是否友好。
    类别：
    - 内容友好
    - 内容不友好
    - 中性内容

    用户输入：$开始.content
    ```

    ![内容友好度检测](./llm-intent/friendly-check.png)

3. **分支处理**
    - 内容友好分支：返回正面回应
    - 内容不友好分支：给出温和提醒
    - 中性内容分支：继续对话

### 2. 客服意图识别

识别客户咨询的具体意图：

```
节点名称：意图识别
模型：GPT-4o
分离提示词：你是一个专业的客服意图分析师，请判断用户输入属于哪种咨询类型
类别：
- 查询订单
- 投诉反馈
- 退款相关
- 其他咨询

用户输入：$开始.message
```

![客服意图识别](./llm-intent/customer-service.png)

## 最佳实践

1. **提示词设计**

    - 提供清晰的分类标准
    - 避免模糊的分类界限

2. **分类优化**

    - 控制分类数量，避免过于复杂
    - 确保分类之间互斥
    - 添加"其他"类别处理边界情况，这样保证工作流不会中断

## 高级用法

### 1. 多级分类

通过连接多个LLM意图分类节点实现更精细的分类：

```
[粗分类] --> [细分类] --> [最终处理]
```

### 2. 组合分析

结合多个维度的分类结果：

```
意图分类：咨询/投诉/建议
情感分类：正面/负面/中性
紧急度分类：普通/紧急/特急
```

## 常见问题

:::caution
常见错误与优化建议：

1. **分类设计优化**

    ```
    ❌ 错误示例：
    - 好/不好
    - 是/否

    ✅ 正确示例：
    - 积极正面的内容
    - 消极负面的内容
    - 中性或客观的内容
    ```

2. **提示词优化**

    ```
    ❌ 错误示例：
    "判断一下这段内容"

    ✅ 正确示例：
    "分析以下内容的情感倾向，只返回POSITIVE/NEGATIVE/NEUTRAL其中之一"
    ```

3. **边界情况处理**
   `    ❌ 错误：没有处理不确定的情况
✅ 正确：添加UNCERTAIN或OTHER类别`
   :::

## 调试技巧

1. **测试数据验证**

    - 准备典型测试用例，让模型充分利用few-shot能力
    - 包含边界情况测试
    - 记录分类准确率, 持续优化提示词

2. **分类结果分析**
    - 使用输出节点记录结果
    - 分析错误分类原因
    - 持续优化提示词

合理使用意图分类节点，可以让工作流程更好地适应不同场景。根据具体业务需求来调整分类规则，让分类结果更准确、更有用。
