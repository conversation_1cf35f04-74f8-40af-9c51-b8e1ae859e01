---
title: 高级模板节点
description: 深入解析FlowAI工作流中的高级模板节点功能, 使用基于Go Template语法根据模板规则动态渲染内容, 支持丰富的内置函数和应用场景。快速实现复杂的文本处理逻辑。
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI教程, AI工作流创建, LLM节点使用, AI自动化, Go Template"
    - tag: title
      content: "FlowAI 高级模板节点 - 工作流高级功能指南 | FlowAI 文档"
---

高级模板节点是FlowAI平台中的一种强大功能，它允许用户使用**Go Template**语法根据模板规则动态渲染内容。通过这个节点，您可以创建复杂的文本处理逻辑，实现内容的条件渲染和格式化。

![高级模板节点](./advance-template/advance.png)

## 基本用法

高级模板节点遵循**Go Template**的语法规则，使您能够：

- 引用变量
- 使用条件语句
- 创建循环
- 应用过滤器和函数
- 处理JSON数据

## 配置步骤

1. **节点命名**：为您的模板节点设置一个有意义的名称，例如"高级模板1"
2. **添加输入参数**：点击"+ 添加"按钮配置所需的输入变量
3. **编写模板内容**：在内容区域使用**Go Template**语法编写您的模板

## 输入参数

您可以在节点配置中添加多个输入参数。这些参数将作为变量在模板中使用：

- 参数名称应简洁明了，如"input1"
- 参数值可以是文本、数字或从其他节点传递的数据
- 在工作流中，可以通过类似`$开始.输入1`这样的方式引用其他节点的输出作为输入

## Go Template语法示例

### 变量引用

```
{{ .varName }}
```

示例：使用`{{ .input1 }}`可以引用名为"input1"的输入参数

### 条件语句

```
{{if .condition}}...{{else}}...{{ end }}
```

**实际示例**：根据用户情绪生成不同回复

```
{{if eq .mood "happy"}}
  很高兴看到您今天心情不错！我们为您推荐以下活动：
  - 户外野餐
  - 与朋友聚会
  - 尝试新的爱好
{{else if eq .mood "tired"}}
  看起来您需要休息。以下是一些放松建议：
  - 冥想15分钟
  - 喝杯温热的花草茶
  - 早点休息
{{else}}
  感谢您的反馈！我们随时为您提供帮助。
{{ end }}
```

### 循环遍历

```
{{range .items}}...{{ end }}
```

**实际示例**：生成待办事项列表

```
# 今日待办事项

{{range .todoItems}}
## {{.title}}
- 优先级: {{.priority}}
- 截止日期: {{.dueDate}}
- 描述: {{.description}}

{{end}}
```

### 管道操作

```
{{ .value | function1 | function2 }}
```

**实际示例**：格式化用户输入

```
原始输入: {{ .userInput }}
格式化后: {{ .userInput | trim | lower }}
```

## 内置函数

高级模板节点提供了丰富的内置函数，可以帮助您处理各种数据转换和操作需求：

### JSON处理函数

| 函数             | 描述                   | 示例                                                                 |
| ---------------- | ---------------------- | -------------------------------------------------------------------- |
| `json_parse`     | 将JSON字符串解析为对象 | `{{ $data := json_parse .jsonString }}{{ $data.name }}`              |
| `json_stringify` | 将对象转换为JSON字符串 | `{{ $obj := dict "name" "张三" "age" 30 }}{{ json_stringify $obj }}` |

**实际示例**：处理API返回的JSON数据

```
{{$apiResponse := json_parse .apiResult}}

# 用户资料

- 姓名: {{$apiResponse.user.name}}
- 邮箱: {{$apiResponse.user.email}}
- 会员等级: {{$apiResponse.user.membershipLevel}}

## 最近订单
{{range $apiResponse.user.orders}}
- 订单号: {{.id}} - 金额: {{.amount}}元 - 状态: {{.status}}
{{end}}
```

### 字符串处理函数

| 函数         | 描述                   | 示例                                                  |
| ------------ | ---------------------- | ----------------------------------------------------- |
| `upper`      | 转换为大写             | `{{ .text \| upper }}`                                |
| `lower`      | 转换为小写             | `{{ .text \| lower }}`                                |
| `trim`       | 去除两端空格           | `{{ .text \| trim }}`                                 |
| `trim_left`  | 去除左侧空格           | `{{ .text \| trim_left }}`                            |
| `replace`    | 替换字符串             | `{{ replace .text "旧文本" "新文本" }}`               |
| `split`      | 分割字符串为数组       | `{{ $arr := split .text "," }}`                       |
| `join`       | 将数组连接为字符串     | `{{ join .tags "、" }}`                               |
| `contains`   | 检查是否包含子串       | `{{ if contains .text "关键词" }}包含关键词{{ end }}` |
| `has_prefix` | 检查是否以指定前缀开始 | `{{ if has_prefix .code "A" }}A类产品{{ end }}`       |
| `has_suffix` | 检查是否以指定后缀结束 | `{{ if has_suffix .email ".com" }}国际邮箱{{ end }}`  |

**实际示例**：格式化客户反馈

```
{{$keywords := split .feedbackTags ","}}

# 客户反馈分析

原始反馈: {{.feedback}}
格式化反馈: {{replace (lower .feedback) "不满意" "需要改进"}}

## 关键标签
{{range $keywords}}
- {{. | trim | upper}}
{{end}}

## 情感分析
{{if contains (lower .feedback) "满意"}}
积极反馈 ✓
{{else if contains (lower .feedback) "失望"}}
消极反馈 ✗
{{else}}
中性反馈 ○
{{end}}
```

### 数学计算函数

| 函数  | 描述 | 示例                         |
| ----- | ---- | ---------------------------- |
| `add` | 加法 | `{{ add .num1 .num2 }}`      |
| `sub` | 减法 | `{{ sub .total .discount }}` |
| `mul` | 乘法 | `{{ mul .price .quantity }}` |
| `div` | 除法 | `{{ div .total .count }}`    |
| `mod` | 取模 | `{{ mod .number 2 }}`        |

**实际示例**：购物车计算

```
# 购物清单

{{range .items}}
- {{.name}}: {{.price}}元 x {{.quantity}} = {{mul .price .quantity}}元
{{end}}

## 费用汇总
- 商品总价: {{.subtotal}}元
- 折扣: {{.discount}}元
- 运费: {{.shipping}}元

## 应付金额
总计: {{add (sub .subtotal .discount) .shipping}}元

{{if gt .discount 0}}
您节省了: {{.discount}}元!
{{end}}
```

### 数组/切片操作函数

| 函数     | 描述                 | 示例                  |
| -------- | -------------------- | --------------------- |
| `first`  | 获取数组第一个元素   | `{{ first .items }}`  |
| `last`   | 获取数组最后一个元素 | `{{ last .items }}`   |
| `length` | 获取长度             | `{{ length .items }}` |

**实际示例**：处理调查结果

```
{{$responses := json_parse .surveyResponses}}

# 调查结果分析

- 总回复数: {{length $responses}}
- 第一位回复者: {{(first $responses).name}}
- 最后一位回复者: {{(last $responses).name}}

## 详细回复
{{range $index, $resp := $responses}}
{{add $index 1}}. {{$resp.name}} ({{$resp.age}}岁)
   问题1: {{$resp.q1}}
   问题2: {{$resp.q2}}

{{end}}
```

## 高级应用场景

### 场景一：智能客服回复生成

```
{{$customerData := json_parse .customerInfo}}

尊敬的{{$customerData.name}}{{if eq $customerData.vipLevel "gold"}}尊贵的金卡会员{{end}}：

感谢您关于"{{.issueTitle}}"的咨询。

{{if contains (lower .issueDescription) "退款"}}
关于您的退款请求，我们将在{{add 1 2}}个工作日内处理。您的订单号{{$customerData.lastOrder.id}}已被记录。
{{else if contains (lower .issueDescription) "配送"}}
您的包裹正在配送中，预计{{.estimatedDelivery}}送达。
运单号：{{$customerData.lastOrder.trackingNumber}}
{{else}}
我们已收到您的反馈，专业客服将尽快与您联系。
{{end}}

{{if gt (length $customerData.pendingOrders) 0}}
您目前有{{length $customerData.pendingOrders}}个待处理订单：
{{range $customerData.pendingOrders}}
- 订单号：{{.id}} | 状态：{{.status}} | 金额：{{.amount}}元
{{end}}
{{end}}

祝您生活愉快！
{{if has_suffix $customerData.name "女士"}}女士{{else}}先生{{end}}
```

### 场景二：数据报告生成

```
{{$salesData := json_parse .monthlySales}}
{{$totalSales := 0}}
{{$bestMonth := ""}}
{{$bestSales := 0}}

# {{.year}}年销售报告

## 月度销售额
{{range $month, $amount := $salesData}}
- {{$month}}: {{$amount}}元
{{$totalSales = add $totalSales $amount}}
{{if gt $amount $bestSales}}
  {{$bestSales = $amount}}
  {{$bestMonth = $month}}
{{end}}
{{end}}

## 销售统计
- 年度总销售额: {{$totalSales}}元
- 月平均销售额: {{div $totalSales 12}}元
- 销售最佳月份: {{$bestMonth}} ({{$bestSales}}元)
- 同比增长: {{if gt .growthRate 0}}↑{{else}}↓{{end}}{{.growthRate}}%

## 产品类别分析
{{range .categories}}
### {{.name}}
- 销售额: {{.sales}}元 (占比: {{mul (div .sales $totalSales) 100}}%)
- 畅销产品: {{.topProduct}}
{{end}}
```

### 场景三：个性化邮件模板

```
{{$user := json_parse .userData}}

主题: {{if eq .emailType "welcome"}}欢迎加入{{.companyName}}{{else if eq .emailType "birthday"}}生日快乐，{{$user.name}}!{{else}}{{.companyName}}最新资讯{{end}}

{{if eq .emailType "welcome"}}
亲爱的{{$user.name}}，

欢迎加入{{.companyName}}大家庭！我们非常高兴您成为我们的一员。

以下是您的账户信息：
- 用户名: {{$user.username}}
- 会员等级: {{$user.membershipLevel}}
- 注册日期: {{$user.registrationDate}}

{{else if eq .emailType "birthday"}}
亲爱的{{$user.name}}，

祝您{{$user.age}}岁生日快乐！

为庆祝您的特殊日子，我们为您准备了{{.birthdayOffer}}优惠券，有效期至{{.offerExpiry}}。

{{else}}
亲爱的{{$user.name}}，

感谢您一直以来对{{.companyName}}的支持。

以下是我们的最新资讯：
{{range .newsItems}}
## {{.title}}
{{.content}}

{{end}}
{{end}}

祝好，
{{.companyName}}团队
```

## 使用输出

高级模板节点的输出可以在工作流中的后续节点中使用：

- 输出引用格式：`$高级模板1.template`
- 您可以将这个输出连接到其他节点作为输入
- 模板处理后的结果可以直接用于邮件发送、API请求或其他操作节点

## 最佳实践

1. **模块化设计**：将复杂模板拆分为多个小模板，提高可维护性
2. **预处理数据**：在模板前使用JSON处理节点整理数据结构
3. **测试验证**：使用测试数据验证模板输出，确保格式正确
4. **注释说明**：在复杂模板中添加注释，提高可读性
5. **错误处理**：使用条件语句处理可能的空值或异常情况

通过合理使用高级模板节点，您可以显著增强您的工作流自动化能力，实现更加灵活和强大的内容处理功能。无论是生成报告、个性化通信还是数据转换，高级模板节点都能满足您的需求。
