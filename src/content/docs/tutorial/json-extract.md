---
title: JSON参数提取节点
description: 深入解析FlowAI中JSON参数提取节点的使用方法，掌握如何从JSON字符串中提取特定数据。通过实际案例学习如何配置JSON数据源和路径，以及如何处理常见的JSON提取问题。
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI JSON参数提取, JSON反序列化, JSON路径提取, 数据处理节点, JSON提取最佳实践, 工作流调试技巧"
    - tag: title
      content: "FlowAI JSON参数提取节点 - 数据处理指南 | FlowAI 文档"
---

JSON参数提取节点是FlowAI中用于处理JSON数据的组件。它能够从上一个节点输出的JSON字符串中提取特定的数据，并将其作为变量传递给后续节点。通过合理使用JSON参数提取节点，你可以轻松处理复杂的JSON数据结构。

:::note
重要说明：JSON参数提取节点要求上一个节点输出的必须是一个合法的JSON字符串。如果上一个节点是"内容拼接器"或"输入节点"，请确保输出的内容可以被JSON解析器解析。如果上一个节点是LLM，必须开启LLM的"JSON输出"功能，以确保整个回复是一个合法的JSON字符串。
:::

## 节点配置

### 基础设置

![JSON参数提取节点基础设置](./json-extract/base-setting.png)

1. **节点名称**

    - 为JSON参数提取节点设置一个描述性名称
    - 建议使用能体现提取目的的名称，如"提取用户信息"、"获取订单详情"等
    - 命名规范：使用动词+名词形式，如"提取用户ID"、"获取订单状态"

2. **JSON数据源**

    - 选择或输入上一个节点的输出变量，如`$LLM1.result`
    - 确保数据源是一个合法的JSON字符串

3. **JSON路径**

    - 输入要提取的JSON路径，如`output.data`、`output.list.1`等
    - 路径格式：使用点号`.`表示层级关系，使用数字表示数组索引（从0开始）

## 输出

- 输出变量的格式为 `$节点名称.output`，例如 `$JSON参数提取1.output`

## 使用示例

### 1. 提取简单JSON数据

让我们从一个简单的JSON字符串中提取数据：

1. 一个简单的JSON字符串

    ```json
    {
    	"output": "Hello, World!"
    }
    ```

2. **JSON参数提取配置**

    ```
    节点名称：提取数据
    JSON数据源：$输入.data
    JSON路径：output
    ```

    ![JSON参数提取配置](./json-extract/simple-extract-node.png)

3. **输出**

    使用提取的数据进行后续处理
    ![输出节点配置](./json-extract/simple-output-node.png)

### 2. 提取嵌套JSON数据

处理嵌套的JSON数据结构：

1. 一个嵌套的JSON字符串

    ```json
    {
    	"output": {
    		"advanceList": [{ "data": "Hello, World!" }]
    	}
    }
    ```

2. **JSON参数提取配置**

    ```
    节点名称：提取嵌套数据
    JSON数据源：$输入.data
    JSON路径：output.advanceList.0.data
    ```

    ![嵌套JSON参数提取配置](./json-extract/nested-extract-node.png)

3. **输出**

    ![输出节点配置](./json-extract/nested-output-node.png)

### 3. 提取数组中的元素

展示如何从JSON数组中提取特定元素：

1. 一个JSON数组

    ```json
    {
    	"output": {
    		"list": ["Hello", "World", "FlowAI"]
    	}
    }
    ```

2. **JSON参数提取配置**

    ```
    节点名称：提取数组元素
    JSON数据源：$输入.data
    JSON路径：output.list.1
    ```

    ![数组元素提取配置](./json-extract/array-extract-node.png)

3. **输出**

    ![输出节点配置](./json-extract/array-output-node.png)

## 高级用法

### 1. 多级提取

你可以通过连接多个JSON参数提取节点来实现多级数据提取，适用于复杂的JSON数据结构：

```
[一级提取] --> [二级提取] --> [三级提取]
```

## 语法规范

JSON参数提取节点采用GJSON路径语法，支持丰富的查询和操作功能。以下是完整语法说明：

### 基础路径

- 使用点号`.`表示层级关系
- 数组索引从0开始计数

```json
{
	"user": {
		"tags": ["AI", "Developer", "GPT"]
	}
}
```

```
user.tags.1  // 提取"Developer"
```

### 通配符

- `*` 匹配任意长度字符
- `?` 匹配单个字符

```
user.t*      // 匹配tags数组
user.ta?s    // 匹配tags数组
```

### 转义字符

对特殊字符使用`\`进行转义：

```json
{
	"file.name": "config.json"
}
```

```
file\.name  // 提取"config.json"
```

### 数组操作

- `#` 获取数组长度
- `#(查询条件)` 数组查询

```json
{
	"users": [
		{ "name": "Alice", "age": 25 },
		{ "name": "Bob", "age": 30 }
	]
}
```

```
users.#            // 返回2（数组长度）
users.#.name       // 返回["Alice","Bob"]
users.#(age>25).name  // 返回"Bob"
```

### 查询语法

支持多种查询运算符：

```json
{
	"products": [
		{ "id": 1, "price": 99, "tags": ["electronics", "sale"] },
		{ "id": 2, "price": 199, "tags": ["furniture"] }
	]
}
```

```
// 比较查询
products.#(price>=100).id    // 返回2
// 模式匹配
products.#(tags%"*sale*").id // 返回1
// 嵌套查询
products.#(tags.#(=="furniture"))#.id // 返回2
```

### 修饰符

通过`@`符号使用数据处理修饰符：

```
products.@reverse    // 反转数组顺序
products.@pretty     // 美化JSON输出
products.@ugly       // 压缩JSON格式
products.@join       // 合并多个对象
```

:::tip
最佳实践

1. 复杂路径建议分步提取，使用多个节点串联
2. 对不确定的路径先用输出节点调试
3. 数组查询优先使用索引定位（当结构稳定时）
4. 含特殊字符的字段名始终使用转义语法
   :::

## 常见问题

:::caution
常见错误：

1. **路径错误**

    ```
    JSON路径：output.data  // ❌ 错误：路径不存在
    ```

    正确做法：

    ```
    JSON路径：output.data  // ✅ 正确：确保路径存在
    ```

2. **数据源非法**

    ```
    JSON数据源：$LLM1.result  // ❌ 错误：数据源不是合法的JSON字符串
    ```

    解决方案：确保数据源是一个合法的JSON字符串，比如要求LLM输出JSON字符串（开启LLM的JSON输出功能）。

3. **数组越界**

    ```
    JSON路径：output.list.5  // ❌ 错误：数组越界
    ```

    解决方案：确保数组索引在有效范围内

4. **性能优化**
    - 避免过多嵌套提取

:::

## 调试技巧

**使用输出节点**
在每个分支添加输出节点，帮助调试流程：

```
[JSON参数提取] --> [输出节点(打印提取结果)]
```

通过合理使用JSON参数提取节点，你可以轻松处理复杂的JSON数据结构。记住要仔细规划提取路径，确保路径的准确性和合法性，这样才能保证工作流程的可靠运行。
