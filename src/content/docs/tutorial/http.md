---
title: HTTP请求节点
description: 深入解析FlowAI HTTP请求节点的使用方法，掌握GET、POST等请求方法，学习API调用、数据处理、错误处理等核心功能。通过实际案例学习如何实现数据获取、接口调用、身份验证等常见场景。
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI HTTP请求, API调用, 网络请求节点, GET请求, POST请求, 接口调试, 数据处理, 错误处理"
    - tag: title
      content: "FlowAI HTTP请求节点 - 网络请求与API调用指南 | FlowAI 文档"
---

HTTP请求节点是FlowAI中用于发送网络请求的基础组件。它支持常见的HTTP方法（GET、POST、PUT、DELETE等），可以轻松地与各种Web API进行交互。

## 节点配置

### 基础设置

1. **节点名称**

    - 设置一个描述性的名称，如"获取天气"、"发送数据"
    - 其他节点可通过此名称引用结果
    - 建议使用能体现功能的名字，方便维护

2. **HTTP地址**
   两种设置方式：

    - **变量引用**：从其他节点获取URL(使用这个模式，你需要将本节点连接在其他节点之后)
    - **直接输入**：手动输入固定URL

3. **请求方法**
   支持标准HTTP方法：

    - GET：获取数据
    - POST：创建数据
    - PUT：更新数据
    - DELETE：删除数据

4. **请求头**
    - 可添加自定义请求头
    - 常用于身份验证、内容类型指定等
    - 格式为key-value对

## 节点输出

节点会输出两个主要内容：

- `$HTTP节点名称.body`：响应主体内容
- `$HTTP节点名称.status`：HTTP状态码

## 使用示例

### 获取UUID示例

让我们通过调用 httpbin.org 的 API 来获取一个 UUID：
![获取UUID](./http/get-uuid.png)

1. **节点配置**

    ```
    节点名称：UUID
    HTTP地址：https://httpbin.org/uuid
    请求方法：GET
    请求头：空
    ```

2. **预期输出**

    ```json
    // $UUID.body 的内容
    {
    	"uuid": "2753d66b-6e6f-4667-8a46-27c4c4f915f6"
    }
    ```

3. **状态码**
    ```
    // $UUID.status 的内容
    200
    ```

你可以使用JSON节点提取UUID值，也可以直接用输出节点打印数据。

### POST数据示例

让我们通过调用 httpbin.org 的 POST API 来发送一些数据：
![POST请求](./http/post-data.png)

1. **节点配置**

    ```
    节点名称：发送数据
    HTTP地址：https://httpbin.org/post
    请求方法：POST
    请求头：
    key: Content-Type
    value: application/json

    请求体：
    {
      "name": "张三",
      "age": 25,
      "message": "$输入节点.message"
    }
    ```

2. **预期输出**

    ```json
    // $发送数据.body 的内容
    {
    	"args": {},
    	"data": "{\"name\":\"张三\",\"age\":25,\"message\":\"你好世界\"}",
    	"files": {},
    	"form": {},
    	"headers": {
    		"Content-Type": "application/json",
    		"Content-Length": "54",
    		"Host": "httpbin.org"
    	},
    	"json": {
    		"name": "张三",
    		"age": 25,
    		"message": "你好世界"
    	},
    	"url": "https://httpbin.org/post"
    }
    ```

3. **状态码**
    ```
    // $发送数据.status 的内容
    200
    ```

在这个例子中，我们：

- 选择了 POST 方法，此时会出现"请求体"输入框
- 设置了 Content-Type 请求头为 application/json
- 在请求体中使用了变量引用 `$输入节点.message`，它会被替换为实际的输入值

### 通用API调用流程

```
[HTTP请求] --------> [JSON处理] --------> [数据展示]
    |                    |                    |
    |                    |                    |
获取原始数据         解析JSON数据         展示处理结果
```

## 注意事项

:::tip
使用建议：

- 检查URL格式是否正确
- 注意请求方法的选择
- 合理设置请求头
  :::

:::caution
常见问题：

- URL无法访问：检查网络连接和地址正确性
- 认证失败：确认认证信息是否正确
- 响应格式错误：检查API文档要求
  :::

## 最佳实践

1. **错误处理**

    - 检查状态码确认请求是否成功
    - 对异常情况进行适当处理

2. **数据处理**
    - 配合JSON节点处理结构化数据
    - 使用LLM节点分析非结构化响应
