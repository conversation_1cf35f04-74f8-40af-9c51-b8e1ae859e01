---
title: 内容拼接节点
description: 深入解析FlowAI内容拼接节点的使用方法，掌握如何将多个节点的输入整合为一个输出。通过实际案例学习如何实现内容复用、变量合并等常见场景。
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI内容拼接, 工作流内容整合, 变量合并, 内容复用, 内容拼接最佳实践, 工作流调试技巧"
    - tag: title
      content: "FlowAI 内容拼接节点 - 内容整合指南 | FlowAI 文档"
---

内容拼接节点是FlowAI中的核心组件之一，它能将多个节点的输入内容整合为一个输出。通过合理使用内容拼接节点，你可以实现内容的复用和变量的合并，从而简化复杂的工作流程。

:::note
重要说明：内容拼接节点支持插入变量，并且可以将多个变量的内容合并为一个输出。这使得它在处理需要整合多个输入的场景时非常有用。
:::

## 节点配置

### 基础设置

1. **节点名称**

    - 为内容拼接节点设置一个描述性名称
    - 建议使用能体现拼接目的的名称，如"合并用户信息"、"整合订单数据"等
    - 命名规范：使用动词+名词形式，如"合并用户信息"、"整合订单数据"

2. **内容设置**

    - 在内容区域中，你可以直接输入文本，并插入变量
    - 变量格式为 `$变量名`，例如 `$LLM1.result` 或 `$输入.input`
    - 支持插入多个变量，并且可以在变量之间添加自定义文本

## 输出

    - 拼接后的内容将作为一个新的变量输出
    - 输出变量格式为 `$节点名称.template`，例如 `$内容拼接器1.template`

## 使用示例

### 1. 合并用户信息

让我们创建一个简单的用户信息合并流程，这是FlowAI中最常用的场景之一：

1. **输入节点配置**

    ![用户信息输入节点配置](./template/user-info-input-node.png)

2. **内容拼接配置**

    ```
    节点名称：合并用户信息
    内容：
    用户姓名：$输入.name
    用户年龄：$输入.age
    用户地址：$输入.address
    ```

    _**注意**：确保在内容拼接节点之前，所有需要引用的变量都已经存在！_

    ![内容拼接配置](./template/user-info-merge-node.png)

3. **输出节点配置**

    使用拼接后的内容进行后续处理
    ![输出节点配置](./template/user-info-output-node.png)

### 2. 整合订单数据

一个实用的例子 - 将多个订单信息整合为一个输出，适用于订单处理、数据分析等场景：

1. **输入节点配置**
   ![订单信息输入节点配置](./template/order-info-input-node.png)

2. **内容拼接配置**

    ```
    节点名称：整合订单数据
    内容：
    订单号：$输入.orderId
    订单金额：$输入.amount
    订单状态：$输入.status
    ```

    ![内容拼接配置](./template/order-info-merge-node.png)

3. **处理节点配置**

    比如你可以：

    - 将整合后的订单数据发送到数据库
    - 将整合后的订单数据发送到邮件系统
    - 将整合后的订单数据发送到报表系统

### 3. 消息模板生成

展示如何使用内容拼接节点生成消息模板，适用于消息推送、通知发送等场景：

```
节点名称：生成消息模板
内容：
亲爱的$输入.name：
您的订单$输入.orderId已$输入.status。
感谢您的支持！
```

![消息模板生成配置](./template/message-template-merge-node.png)

## 高级用法

### 1. 嵌套拼接

你可以通过连接多个内容拼接节点来实现复杂的逻辑，适用于多级内容整合、复杂业务规则等场景：

```
[用户信息拼接] --> [订单信息拼接] --> [消息模板生成]
```

### 2. 组合拼接示例

在一个拼接节点中使用多个变量和自定义文本，适用于需要同时整合多个变量的场景：

```
节点名称：生成报告
内容：
报告日期：$输入.date
用户总数：$input.userCount
订单总数：$input.orderCount
总销售额：$input.totalSales
```

## 最佳实践

1. **内容设计原则**

    - 注意变量的命名规范
    - 确保变量在拼接节点之前已经存在
    - 使用注释说明每个变量的业务含义
    - 保持内容逻辑简单可维护

2. **变量处理**

    - 在内容拼接前先确保变量存在
    - 考虑数据类型的一致性
    - 注意变量的作用域

3. **错误处理**
    - 添加默认值处理异常情况
    - 在关键拼接节点添加日志记录
    - 考虑数据验证的必要性

## 常见问题

:::caution
常见错误：

1. **变量问题**

    用户并没有提供name这个变量在`输入节点`中，但是内容拼接节点中需要使用这两个变量。请务必确保所有变量都已存在，且变量名称正确。**尤其是修改了节点名称的情况下！**

    ```
    内容：
    用户姓名：$input.name
    用户年龄：$input.age
    // ❌ 错误：如果$input.name不存在，会导致拼接失败，显示为`$input.name`。
    ```

2. **遗漏变量**

    ```
    内容：
    用户姓名：$input.name
    用户年龄：$input.age
    // 遗漏了用户地址
    ```

    正确做法：确保所有需要拼接的变量都已包含

3. **变量大小写敏感**

    ```
    内容：
    用户姓名：$input.Name  // 不会匹配 $input.name
    ```

    解决方案：注意变量名称的大小写

4. **性能优化**
    - 避免过多嵌套拼接

:::

## 调试技巧

**使用输出节点**
在每个拼接节点后添加输出节点，帮助调试流程：

```
[内容拼接] --> [输出节点(打印拼接结果)]
```

通过合理使用内容拼接节点，你可以构建出灵活而强大的工作流程。记住要仔细规划拼接逻辑，确保变量的完整性和一致性，这样才能保证工作流程的可靠运行。
