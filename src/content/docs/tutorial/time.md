---
title: 当前时间节点
description: 了解如何在FlowAI中使用时间节点获取系统当前时间，为LLM提供精确的时间上下文。掌握时间格式、使用场景和最佳实践。
head:
    - tag: title
      content: "FlowAI 当前时间节点 - 获取系统当前时间 | FlowAI 文档"
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI时间节点, 系统当前时间, 时间上下文, 自动化工具, LLM时间处理, 智能时间管理"
---

**当前时间节点** 是FlowAI中用于为LLM提供时间上下文的实用组件。通过这个AI工作流工具，您可以轻松获取精确到秒的当前时间，帮助LLM在对话和任务处理中准确理解时间信息。无论是自动化流程、智能对话系统还是数据分析，时间节点都能为您的AI应用提供可靠的时间参考。

## 节点配置

![时间节点](./time/get-time.png)

### 基础设置

1. **节点名称**

    - 设置一个描述性的名称，如"当前时间"、"系统时间"
    - 其他节点可通过此名称引用时间结果
    - 建议使用能体现功能的名字，方便维护

## 节点输出

节点会输出一个主要内容：

- `$时间节点名称.time`：当前系统时间

**时间格式**

- 格式：`YYYY-MM-DD HH:mm:ss`
- 示例：`2025-01-02 03:04:05`

## 使用示例

### 为LLM提供时间上下文

时间节点的主要用途是为LLM提供准确的时间信息，帮助其更好地理解用户请求：

1. **天气查询场景**

    ```
    用户：今天天气怎么样？
    LLM：今天是2025-01-02，根据当前时间03:04:05，为您查询今日天气...
    ```

2. **数据查询场景**

    ```
    用户：帮我查询这个月的订单数据
    LLM：当前时间是2025年1月2日，正在为您查询2025年1月的订单数据...
    ```

3. **日程安排场景**

    ```
    用户：提醒我明天下午开会
    LLM：当前时间是2025-01-02 15:00:00，已为您设置2025-01-03 15:00的会议提醒
    ```

## 注意事项

:::tip
使用建议：

- 合理命名时间节点以便后续引用
  :::

:::caution
常见问题：

- 格式不符：检查时间格式要求
  :::

## 最佳实践

1. **LLM上下文支持**

    - 为LLM提供准确的时间参考
    - 支持时间相关的推理和决策

2. **动态查询生成**

    - 帮助LLM生成基于当前时间的动态查询语句
    - 如SQL查询、API请求等

3. **时间敏感任务**

    - 支持时间相关的任务处理
    - 如日程安排、定时提醒等

## 常见问题

### 时间节点的主要用途是什么？

时间节点主要用于为LLM提供准确的时间信息，帮助AI系统更好地理解用户请求的时间上下文。

### 时间节点的输出格式是什么？

时间节点输出标准的`YYYY-MM-DD HH:mm:ss`格式，例如：`2025-01-02 03:04:05`。

### 如何在FlowAI中使用时间节点？

只需在FlowAI工作流中添加时间节点，设置节点名称后即可在其他节点中通过`$节点名称.time`引用当前时间。

## 典型应用场景

### 1. 智能客服系统

- 提供准确的时间参考
- 支持时间相关的用户查询
- 实现基于时间的个性化回复

### 2. 数据分析流程

- 为数据打上时间戳
- 支持时间序列分析
- 实现动态数据查询
