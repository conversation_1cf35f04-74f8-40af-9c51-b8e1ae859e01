---
title: 输入节点
description: 深入解析FlowAI工作流中的Input输入节点功能，包括变量配置、输入类型设置、节点属性管理等，助您快速掌握工作流数据输入的最佳实践。
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI输入节点,工作流输入配置,变量管理,输入类型设置,节点属性,工作流开发"
    - tag: title
      content: "FlowAI 输入节点 - 工作流数据输入指南 | FlowAI 文档"
---

Input节点作为工作流的首个节点，扮演着接收外部输入和工作流入口的关键角色。它在工作流创建时自动添加，且不可删除，保证了数据流的起始点。本文将详细介绍Input节点的各项功能和使用技巧。

## 节点属性详解

![节点属性编辑](./input/input-node-setting.png)

1. **节点名称**：可自定义的输入节点标识。修改后会影响工作流中其他节点对该节点的引用。例如，将"开始"改为"Input1"后，其他节点的引用将从`$开始.xxx`变为`$Input1.xxx`。

2. **变量编辑区**：用于定义和管理工作流中的关键变量，支持多种数据类型配置。

3. **添加新变量按钮**：快速在变量编辑区增加新变量，提升工作效率。

## 变量操作区详解

### 基础操作

![变量基础操作区](./input/input-node-variable-area-basic-operation.png)

### 类型修改

通过下拉菜单可灵活调整变量类型。

![变量类型修改](./input/input-node-variable-area-change-type.png)

### 顺序调整

拖拽功能允许自由调整变量顺序，优化用户输入体验。

![变量拖拽](./input/input-node-variable-area-drag.png)

## 输入类型多样化

### 文本/长文本

适用于收集用户的文字信息，支持短文本和长文本的样式，满足不同场景的输入需求。

![文本输入](./input/input-node-variable-area-text.png)

### 单选框/下拉框

为用户提供预设选项，确保至少有一个可选项，适用于固定选项选择场景。

- 支持添加、删除选项
- 可点击"默认"按钮设置默认选中项（仅限一个）
- 提供清晰的选项展示和选择体验

![单选框/下拉框](./input/input-node-variable-area-select.png)

运行器中的实际效果：

![单选框/下拉框运行器](./input/input-node-variable-area-select-runner.png)

### 复选框

允许用户选择多个选项，适用于多关键词场景，支持灵活的多选操作。

![复选框](./input/input-node-variable-area-checkbox.png)

运行器中的实际效果：

![复选框运行器](./input/input-node-variable-area-checkbox-runner.png)

:::note
在FlowAI中，所有输入最终都以文本形式存储。多选项结果将以逗号分隔的字符串返回，如`科幻,爱情`，便于后续节点处理。
:::

## 输入节点的使用

Input节点中配置的变量会在运行器中渲染为相应的输入控件。用户输入的信息将被保存到这些变量中，供工作流中的其他节点使用。

变量引用语法：`$节点名称.变量名称`

更多关于变量语法的详细信息，请参阅[变量语法指南](/tutorial/variable/)。

通过合理设置和使用Input节点，您可以为整个工作流奠定坚实的数据基础，确保后续节点能够有效地处理和转换这些输入数据。

<a href="https://flowai.cc/dashboard/projects" target="_blank" rel="noopener noreferrer">立即体验FlowAI，开启您的AI工作流自动化之旅！</a>
