---
title: FlowAI Prompt变量使用指南
description: 全面解析FlowAI Prompt变量语法，掌握变量定义与引用技巧，提升AI工作流效率。包含详细示例与图解，适合初学者与进阶用户。
head:
    - tag: title
      content: "FlowAI Prompt变量使用指南 | 变量语法详解 | FlowAI官方文档"
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI Prompt变量,AI变量语法,提示词变量,工作流变量,AI自动化,FlowAI教程"
---

首先需要明白2个概念：

1. **Prompt(提示词)是什么?**

    想象你在跟一个非常聪明的朋友聊天。Prompt就像是你对这个朋友说的话。通过巧妙地组织你的话语(Prompt),你可以引导这个朋友给出你想要的回答。在人工智能世界里,Prompt就是你对AI助手说的话,用来指导它该如何回答。

2. **变量是什么?**

    把变量想象成填空题中的空格。当你写Prompt时,可以在里面留一些"空格"。这些"空格"就是变量。等到实际使用的时候,你再把具体的内容填到这些"空格"里。这样做的好处是,你可以重复使用同一个Prompt,只需要改变填入"空格"的内容,就能得到不同的结果。

## 变量语法详解

### 变量定义与使用

在FlowAI中，我们使用 `$`符号来表示一个变量。这种语法设计简单直观，易于记忆和使用。

整个变量的标准构造方式为：

```
$节点名称.节点输出
```

例如，一个LLM节点的典型输出：

```
$LLM1.result
```

其中：

- `LLM1`：节点名称
- `result`：该节点的标准输出字段

每个节点的输出内容可能不同，具体输出字段可以在**节点详情面板**中查看。
![节点输出内容示例](./variable/node-output.png)

### 变量引用技巧

在FlowAI工作流中，下游节点可以直接引用上游节点的输出内容，实现数据传递和自动化处理。

典型应用场景：

1. 将LLM1的输出作为LLM2的输入
2. 将数据处理节点的结果传递给分析节点
3. 实现多步骤AI任务的串联

![上游节点下游节点](./variable/upstream-downstream.png)

引用方法：

1. 在编辑器中输入`$`符号
2. 从自动提示列表中选择需要的上游节点变量
3. 系统会自动生成完整的变量引用格式

![变量引用示意图](./variable/downstream-node.png)
