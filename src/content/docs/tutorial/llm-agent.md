---
title: LLM Agent节点
description: 详细介绍LLM Agent节点的配置、使用方法和最佳实践，帮助您充分利用智能体的自动化能力
head:
    - tag: title
      content: "FlowAI LLM Agent节点 - 智能体工作流集成与自动化 | FlowAI 文档"
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI LLM Agent节点, 智能体, Agent, GPT, 工具调用, 自动化, FlowAI, 工作流"
---

LLM Agent节点是**FlowAI智能工作流**中的高级组件，它不同于普通的LLM节点。LLM Agent作为一个**智能体**，能够**自动调用工具**完成复杂任务，实现更高级的自动化处理能力。

## LLM Agent节点基础配置

### 节点属性详解

![节点属性](./llm-agent/llm-agent-node-setting.png)

1. **节点名称**：可自定义的LLM Agent节点标识。修改后会影响工作流中其他节点对该节点的引用。例如，将"LLMAgent1"改为"智能助手"后，其他节点的引用将从`$LLMAgent1.result`变为`$智能助手.result`。建议使用有意义的名称，以便于理解和维护。

2. **模型**：通过下拉菜单选择要使用的语言模型。目前支持：

    - **GPT-4.1**：最新的高性能模型，支持完整的Agent能力（**现已全面支持**）
    - GPT-4o：性能与成本的良好平衡
    - GPT-4o-mini：性能与成本的平衡
    - DeepSeek：最经济、性价比最高的模型
    - 其他模型：根据实际需求选择

3. **提示词编辑区**：用于编写发送给智能体的指令。您可以：

    - 使用变量语法引用其他节点的输出
    - 编写详细的任务描述
    - 设置期望的输出格式

4. **输出语言**：设置Agent的回复语言，支持中文、英文等多种语言选项（这个的输出结果取决于LLM的智能程度）。

5. **工具**：Agent可以调用的工具集合，目前支持：

    - 网页抓取：自动获取网页内容
    - 计算器：执行数学计算
    - HTTP请求：发送API请求获取数据

6. **最大连代轮数**：Agent执行工具调用的最大次数，范围1-50。

7. **MCP服务器**：通过配置MCP（Model Context Protocol）服务器，扩展Agent的能力能，允许Agent通过MCP协议调用外部服务和工具。

## LLM Agent与LLM节点的区别

| 特性       | LLM Agent节点                | LLM节点                  |
| ---------- | ---------------------------- | ------------------------ |
| 功能定位   | 智能体，可自主决策并调用工具 | 单次AI推理，生成文本内容 |
| 工具调用   | 支持自动调用多种工具         | 不支持工具调用           |
| 任务复杂度 | 适合复杂、多步骤任务         | 适合单一文本生成任务     |
| 自主性     | 高，可根据任务自主选择工具   | 低，严格按提示词执行     |
| 应用场景   | 数据收集、分析、自动化处理   | 内容生成、文本分析       |

## 提示词编写技巧

与LLM节点相比，LLM Agent节点的提示词更侧重于**任务描述**和**目标定义**，而非具体详细的执行步骤。

### 基础语法

```
请帮我在http://xxxx.com查询"$输入.关键词"的最新信息，并提供简要总结。
```

### 多步骤任务示例

```
请执行以下任务：
1. 查询"$输入.股票代码"的股票价格
2. 计算过去7天的平均价格
3. 如果平均价格高于$输入.阈值，则发送HTTP请求到我们的通知系统

备注：
股票的查询方式是：
1. 访问 https://xxxx.com/stock/xxxx
2. 其中 xxx 是股票代码
```

### 数据处理示例

```
请分析以下数据：$输入.数据表
要求：
1. 计算每列的平均值
2. 找出异常数据点（超过两个标准差）
3. 生成分析报告，包含关键发现和建议
```

## 工具使用详解

LLM Agent节点可以调用多种工具，每种工具有其特定的使用场景和能力：

### 网页抓取工具

网页抓取工具允许Agent自动获取网页内容，适用于：

- 收集最新新闻或信息
- 获取产品价格或规格
- 提取网页上的结构化数据

**示例提示词**：

```
请抓取"$输入.网址"页面，提取所有产品名称和价格，并整理成Markdown表格输出。
```

### 计算器工具

计算器工具使Agent能够执行数学计算，适用于：

- 数据分析和统计
- 财务计算
- 科学计算和转换

目前计算工具支持四则运算、三角函数、Pi、指数等基础计算语法。

**示例提示词**：

```
请计算以下数据的统计指标：$输入.数值列表
需要计算：平均值、中位数、标准差、四分位数
```

### HTTP请求工具

HTTP请求工具允许Agent与外部API交互，适用于：

- 数据同步和集成
- 触发外部系统操作
- 获取第三方服务数据

**示例提示词**：

```
请将以下处理结果：$输入.处理结果
通过HTTP POST请求发送到我们的API端点：$输入.API地址
请确保使用正确的JSON格式。
```

## 高级配置详解

### 最大连代轮数设置

最大连代轮数控制Agent可以理解为执行的工具调用次数，但是并不是一一对应，取决于AI如何判断。因为对于某些可以并行任务，有可能一轮迭代就会同时调用多个工具。

1. **参数说明**：

    - 范围：1-50
    - 默认值：通常为3-5

2. **设置建议**：

    - 简单任务：3-5轮
    - 复杂任务：10-15轮
    - 特殊场景：根据需求调整

3. **注意事项**：
    - 轮数过多可能增加执行时间、也可能导致LLM的上下文超过AI能支持的上下文大小
    - 轮数过少可能导致任务无法完成
    - 建议判断自己的任务，选择一个合理的任务轮数

### MCP服务器工具

MCP（Model Context Protocol）服务器工具是LLM Agent节点的新增功能，它允许Agent通过标准化协议调用外部服务和工具，大大扩展了Agent的能力边界。

#### MCP服务器配置

![MCP服务器配置](./llm-agent/mcp-server-config.png)

**配置步骤**：

1. **添加MCP服务器**：点击"添加MCP服务器"按钮
2. **服务器名称**：为MCP服务器设置一个描述性名称，一个好的名称，更方便AI理解
3. **服务器地址**：输入MCP服务器的URL地址
4. **服务器类型**：选择合适的连接类型：
    - **SSE**：Server-Sent Events，适用于实时数据流
    - **Stream HTTP**：流式HTTP连接，适用于大多数MCP服务
5. **自定义HTTP头**：如需要，可添加认证或其他自定义头部

#### MCP服务器使用示例

**基础配置示例**：

```
服务器名称：fetch服务
服务器地址：https://remote.mcpservers.org/fetch/mcp
服务器类型：Stream HTTP
```

**提示词示例**：

```
告诉我今天GitHub的第一个热门项目
```

**执行过程**：

1. Agent接收到任务后，会自动识别需要获取GitHub热门项目信息
2. 调用配置的fetch MCP服务，抓取GitHub Trends页面
3. 解析页面内容，提取第一个热门项目信息
4. 返回项目名称、描述、星标数等详细信息

#### MCP服务器的优势

- **标准化协议**：基于统一的MCP协议，确保兼容性
- **扩展性强**：可以连接各种外部服务和API
- **配置灵活**：支持多种连接类型和自定义配置
- **安全可靠**：支持认证和加密传输

## 节点输出使用

LLM Agent节点的输出通过 `$LLMAgent节点名称.result` 访问，例如： `$LLMAgent1.result`

输出内容通常包括：

- Agent的思考过程
- 工具调用记录
- 最终结论或处理结果

## 常见应用场景

- **数据收集与分析**：自动从多个网站收集数据，进行计算和分析，生成报告
- **自动化客户服务**：根据客户问题自动查询知识库、计算价格或发送通知
- **内容聚合与处理**：收集多源信息，整合分析后生成摘要或报告
- **智能监控系统**：定期检查数据源，执行计算判断，触发相应操作
- **自动化工作流**：接收输入数据，调用多个工具处理，输出结构化结果
- **MCP服务集成**：通过MCP服务器连接各种外部工具和服务，实现更强大的自动化能力

### MCP服务器应用场景

通过配置不同的MCP服务器，LLM Agent可以实现更多专业化功能：

1. **网页内容抓取**：

    - 配置fetch MCP服务，自动抓取网页内容
    - 实时获取新闻、价格、数据等信息
    - 支持复杂的网页解析和数据提取

2. **API服务调用**：

    - 连接各种第三方API服务
    - 自动处理认证和数据格式转换
    - 实现跨平台数据同步

3. **专业工具集成**：
    - 连接专业分析工具
    - 调用特定领域的计算服务
    - 扩展Agent的专业能力，比如你可以让AI Agent去部署网页、查看Paypal信息等

## 使用建议与最佳实践

1. **任务定义**：

    - 明确定义任务目标和期望输出
    - 分解复杂任务为可管理的步骤
    - 提供足够的上下文信息

2. **工具选择**：

    - 只启用任务所需的工具（因为启用的工具越多、越费token）
    - 考虑工具调用的顺序和依赖关系
    - 为工具提供清晰的使用指南

3. **性能优化**：

    - 合理设置最大连代轮数
    - 避免过于开放或模糊的指令
    - 提供结构化的输入数据

4. **调试技巧**：

    - 从简单任务开始测试
    - 观察Agent的工具调用过程
    - 根据执行结果优化提示词
    - 记录成功案例作为模板
    - 测试MCP服务器连接和响应

5. **MCP服务器最佳实践**：

    - 选择可靠的MCP服务提供商
    - 定期检查服务器状态和可用性
    - 合理设置超时和重试机制
    - 监控MCP服务的使用情况和成本

通过合理配置和使用LLM Agent节点，您可以实现高度自动化的智能工作流，大幅提升处理复杂任务的效率。更多关于变量语法的详细信息，请参阅[变量语法指南](/tutorial/variable/)。

:::tip
在使用LLM Agent节点时，建议先使用简单任务进行测试，熟悉Agent的工作方式后再逐步增加复杂度。同时，密切关注Agent的工具调用过程，确保其按预期执行。
:::
