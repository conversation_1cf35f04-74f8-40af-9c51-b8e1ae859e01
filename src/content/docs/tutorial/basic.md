---
title: FlowAI 快速入门指南
description: 手把手教你使用FlowAI平台，通过简单5步创建AI工作流。学习如何连接LLM节点、调试工作流、保存和使用工作流。适合AI初学者快速上手。
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI教程, AI工作流创建, LLM节点使用, AI自动化, GPT-4o集成"
    - tag: title
      content: "FlowAI 快速入门指南 - 5分钟创建你的第一个AI工作流 | FlowAI 文档"
---

快速开始，创建你的第一个AI工作流

## 1. 登录FlowAI平台

1. 打开浏览器，访问 [FlowAI 平台](https://flowai.cc)
2. 点击右上角的登录/注册按钮。如果已经有账号，可以输入您的邮箱和密码点击登录。如果没有账号，目前仅支持使用Github或者Google登录。比如点击使用**Google登录**。
   ![登录页面](./basic/login.png)

## 2. 创建工作流：从零到一的完整指南

### 2.1 新建空白工作流

如果是首次登录后，会看到一个欢迎页面，点击**创建工作流**。
![创建工作流](./basic/create-workflow.png)
对于已经创建工作流的用户，可以直接在**FlowAI后台管理面板**的任意页面，点击顶部的**新建工作流**按钮，创建新的工作流。

### 2.2 工作流面板

打开工作流编辑面板后，会出现如下界面：

- 标识1: 工作流的**入口节点**，所有工作流都必须从这里开始，这个节点不可删除。
- 标识2: 工作流的画布控制按钮，你可以放大、缩小、锁定等操作。
- 标识3: 工作流的画布节点的地图，你可以从这里看到所有节点的分布情况，适用于快速定位。
- 标识4: 保存工作流、调试工作流的按钮，编辑好的工作流，要点击保存按钮，才会保存到后台。调试按钮用于测试当前编辑的工作流，是否满足预期。
- 标识5: 这个是工作流节点的"添加"按钮，除了"输出节点"外，其他所有节点的末尾都有这个按钮，点击后会出现一个下拉菜单，你可以选择需要的节点类型，点击后会自动在画布中添加该节点。

![工作流面板](./basic/workflow-panel.png)

### 2.3 编辑输入节点

**输入节点**是整个工作流的**入口**，可以用于接收用户输入的内容。下面介绍如何编辑输入节点。

直接点击**输入节点**，会在右边弹出编辑属性。
![编辑节点](./basic/edit-node.png)

在这个例子中，我们直接点击**输入节点**，然后在**输入节点**的编辑面板中，添加新的输入`用户名`。
![输入节点编辑](./basic/input-node-edit.png)

然后关闭编辑面板，你会看到在画布中，输入节点右边的输入节点，会出现一个**用户名**的内容。
![输入节点编辑2](./basic/input-node-edit-2.png)

### 2.4 添加LLM节点

点击输入节点右边的 `+` 按钮，在下拉菜单中选择**LLM**。
![添加LLM节点](./basic/add-llm-node.png)

页面上将会添加一个**LLM节点**，并且会自动连接到输入节点。
![添加LLM节点](./basic/add-llm-node-2.png)

点击**LLM节点**，在右边弹出编辑属性。
在这个例子中，我们选择**LLM**为`GPT-4o`，**Prompt**为`我是 $开始.用户名 ，对我说你好吧`。
![LLM节点编辑](./basic/llm-node-edit.png)

:::note

Prompt 模板中，我们使用 `$开始.用户名` 来表示输入节点中的**用户名**。
你可以输入`$`符号，然后在弹出的下拉菜单中选择你想要的变量。
你可以点击这里查看[变量语法](/tutorial/variable/)。
:::

### 2.5 添加输出节点

点击**LLM节点**右边的 `+` 按钮，在下拉菜单中选择**输出**。
页面上将会添加一个**输出节点**，并且会自动连接到**LLM节点**。
![添加输出节点](./basic/add-output-node.png)
_\*你可以通过输入关键词，来搜索你想要的节点。_

点击**输出节点**，在右边弹出编辑属性。
在这个例子中，我们选择输出为`LLM1.result`。表示将LLM节点的输出结果，作为输出内容。
![输出节点设置](./basic/output-node-setting.png)

## 3. 调试与测试工作流

点击工作流编辑面板上方的**调试**按钮，会弹出一个调试面板。
在这个例子中，我们输入**用户名**为`张三`，然后点击**开始运行**按钮。
![调试工作流](./basic/debug-workflow.png)

在运行过程中，你可以看到在日志中，会打印出当前工作流的运行状态。
![调试结果](./basic/debug-result.png)

当工作流运行完成后，在下方，会显示工作流的运行结果。
![调试结果2](./basic/debug-result2.png)

## 4. 保存与部署工作流

当你认为工作流能按照你的预期运行后，可以点击**保存**按钮，将工作流保存到自己的账户中。
![保存工作流](./basic/save-workflow.png)

## 5. 管理你的工作流

当工作流保存后，你可以在**FlowAI后台管理面板**的**工作流列表**中，看到你保存的工作流。
![工作流列表](./basic/workflow-list.png)

点击**运行**按钮，可以在不打开工作流编辑面板的情况下，运行工作流，方便后续使用。
![运行工作流](./basic/run-workflow.png)

## 常见问题

### Q1: 如何选择合适的LLM模型？

A: 根据你的需求选择模型，GPT-4o适合通用场景，其他模型可能更适合特定任务。当然，你也应该考虑成本因素，DeepSeek 模型也是不错的选择，它以较低的成本，提供了接近GPT-4o的性能。

### Q2: 工作流可以分享给其他人使用吗？

A: 暂时不支持，后续会支持。

### Q3: 如何监控工作流的运行状态？

A: 在后台管理面板中，你可以查看每个工作流的运行日志和统计数据。

## 进阶技巧

- 使用变量模板实现动态内容
- 配置条件分支实现复杂逻辑
- 设置定时任务自动化工作流
