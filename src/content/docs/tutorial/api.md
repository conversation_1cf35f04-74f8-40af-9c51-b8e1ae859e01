---
title: 工作流 API
description: 手把手教你如何在FlowAI中发布、管理和调用工作流API
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI教程, API调用, 工作流发布, API密钥, 流式输出"
    - tag: title
      content: "FlowAI 工作流 API 使用指南 | FlowAI 文档"
---

FlowAI工作流API允许您将设计好的工作流程以API形式对外提供服务，便于集成到您的应用或系统中。本指南将帮助您了解如何发布API、管理API密钥以及如何调用工作流API。

## 第一部分：发布与管理API

### API发布流程

要将FlowAI工作流发布为API，请按照以下步骤操作：

1. 进入您想要发布的工作流编辑页面
2. 完成工作流设计并确保功能正常
3. 点击顶部功能区的**发布API**按钮
   ![发布按钮](./api/publish-api-btn.png)
4. 点击**保存并发布当前工作流**按钮
   ![发布页面](./api/publish-api.png)
5. 在弹出的窗口中确认操作："此操作将保存当前工作流并将其发布为最新版本"
6. 发布成功后，您的工作流即可通过API调用
   ![API管理页面](./api/api-console.png)

### API密钥管理

API密钥是调用FlowAI工作流API的身份凭证，请按照以下步骤管理您的API密钥：

1. 在左侧导航栏中点击**API管理**选项
2. 在API管理页面，您可以：
    - 查看现有的API密钥
    - 创建新的API密钥
    - 查看已发布的工作流列表及其状态

> **重要提示**：首次使用API功能时，您需要创建API密钥。点击"创建新密钥"按钮生成新的API密钥。请妥善保管您的API密钥，不要在公开场合分享。

## 第二部分：API调用指南

### 基本调用格式

FlowAI工作流API采用REST风格，基本调用格式如下：

```bash
curl -X POST https://flowai.cc/v1/api/workflow/run/:workflow_ID \
-H 'X-API-KEY: <KEY>' \
-d '{"input":<输入参数>}'
```

其中：

- `:workflow_ID`：您的工作流ID（在API管理页面可以找到）
- `<KEY>`：您的API密钥
- `<输入参数>`：根据工作流输入节点定义的参数对象

### 参数说明

API请求的主要参数包括：

1. **输入参数**：根据工作流设计，不同的工作流需要不同的输入参数

    - 图像处理工作流示例：`{"input":{"url":"http://example.com/image.jpg"}}`
    - 文本处理工作流示例：`{"input":{"text":"需要处理的文本内容"}}`
    - 对话工作流示例：`{"input":{"输入":"在吗？"}}`

2. **流式输出参数**：添加`"stream":true`启用流式输出
    - 标准调用：`{"input":<输入参数>}`
    - 流式调用：`{"input":<输入参数>,"stream":true}`

### 返回格式

FlowAI工作流API支持两种返回模式：标准输出和流式输出。

#### 1. 标准输出

标准输出模式下，API会在处理完成后一次性返回结果：

**请求示例：**

```bash
curl -X POST https://flowai.cc/v1/api/workflow/run/:workflow_ID \
-H 'X-API-KEY: <KEY>' \
-d '{"input":{"输入":"在吗？"}}'
```

**返回示例：**

```json
{
	"output": "在呢。",
	"total_time": 9.316849339000001
}
```

返回字段说明：

- `output`：工作流的最终输出结果
- `total_time`：工作流执行总耗时（秒）

#### 2. 流式输出

流式输出模式适用于包含大型语言模型（LLM）的工作流，能够实时返回处理过程和结果：

**请求示例：**

```bash
curl -X POST https://flowai.cc/v1/api/workflow/run/:workflow_ID \
-H 'X-API-KEY: <KEY>' \
-d '{"input":{"输入":"在吗？"},"stream":true}'
```

**返回示例（Server-Sent Events格式）：**

```
event:msg
data:{"Node":"myinput","Msg":"start","Type":"start","Data":{"输入":"在吗？"}}

event:msg
data:{"Node":"myinput","Msg":"Start node 开始, type in","Type":"start_node","Data":null}

event:msg
data:{"Node":"myinput","Msg":"Finish node 开始, type in","Type":"finish_node","Data":{"time_spend":0.000125251}}

event:msg
data:{"Node":"1741675999864","Msg":"Start node LLM1, type llm","Type":"start_node","Data":null}

event:stream
data:{"Content":"嗯","Node":"1741675999864","Reasoning":null}

event:stream
data:{"Content":"，","Node":"1741675999864","Reasoning":null}

event:stream
data:{"Content":"在","Node":"1741675999864","Reasoning":null}

event:stream
data:{"Content":"呢","Node":"1741675999864","Reasoning":null}

event:stream
data:{"Content":"。","Node":"1741675999864","Reasoning":null}

event:msg
data:{"Node":"1741675999864","Msg":"Finish node LLM1, type llm","Type":"finish_node","Data":{"time_spend":4.758483707}}

event:msg
data:{"Node":"1741676038250","Msg":"Start node 结束（输出）1, type out","Type":"start_node","Data":null}

event:msg
data:{"Node":"1741676038250","Msg":"Finish node 结束（输出）1, type out","Type":"finish_node","Data":{"time_spend":0.000128861}}

event:output
data:{"output":"嗯，在呢。","total_time":4.758748069}
```

## 第三部分：事件类型与示例应用

### 流式输出事件类型

流式输出中包含三种主要事件类型：

1. **`event:msg`**：节点运行日志

    - `start`：工作流开始运行
    - `start_node`：节点开始执行
    - `finish_node`：节点执行完成，包含耗时

2. **`event:stream`**：LLM流式输出

    - `Content`：当前生成的文本片段
    - `Node`：生成该内容的节点ID
    - `Reasoning`：推理过程（如适用）

3. **`event:output`**：最终输出结果

    - `output`：完整输出内容
    - `total_time`：总耗时

4. **`event:error`**：错误的情况会返回

## 第四部分：最佳实践与常见问题

### API调用实战建议

#### 异常情况应对

- 遇到接口报错别慌，先看状态码：
    - 401：检查密钥是不是复制错了
    - 429：说明触发限流，该歇会儿再调了
    - 5xx：服务器开小差，过几分钟再试
- 网络波动时建议：
    - 给请求加个3次重试（间隔用指数退避）
    - 给用户展示「服务暂时打盹，正在努力唤醒」的友好提示
    - 记录错误日志时记得分类：网络问题/参数错误/服务异常

#### 密钥安全指南

密钥保管要像对待银行卡密码：

- 前端项目里不要写死密钥，比如

    ```javascript
    // 反面教材 ❌
    const API_KEY = "sk-123456...";

    // 正确姿势 ✅
    const API_KEY = process.env.API_KEY;
    ```

- 推荐用后端做中间人转发请求
- 定期更新密钥

#### 提升响应速度

流式对话场景优化技巧：

- 使用专业的SSE客户端库（比如Python的sseclient）
- 高频调用时：
    - 设置本地缓存（相同参数请求缓存5分钟）
    - 配置CDN加速静态资源
    - 用漏桶算法控制请求频率（例如每秒不超过20次）

#### 参数校验

我们的Workflow不会做内容的校验，所以在调用前，保证输入的内容是合法的。

1. 格式验证：
    - 必填字段用白名单校验
    - 用正则表达式检查邮箱/手机号格式
    ```json
    "input": {
      "email": "<EMAIL>",  // 正则：^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$
      "phone": "13800138000"
    }
    ```
2. 内容过滤：
    - 转义特殊字符（<>&等）
    - 敏感词过滤（政治/广告/辱骂内容）
3. 边界处理：
    - 文本长度截断（超过500字提示修改）
    - 图片尺寸限制（建议不超过5MB）

### 常见问题解答

**Q: 如何获取工作流ID？**  
A: 在API管理页面的已发布工作流列表中可以找到每个工作流的ID，你可以一键复制ID和调用的代码。

**Q: 如何处理API调用中的错误？**  
A: API会返回标准HTTP错误码，您应当根据错误码采取相应措施。常见错误包括认证失败(401)、参数错误(400)等。

**Q: 可以使用其他编程语言调用API吗？**  
A: 可以，FlowAI API是基于标准HTTP协议的REST API，可以使用任何支持HTTP请求的编程语言调用。
