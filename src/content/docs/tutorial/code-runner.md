---
title: 代码执行器节点
description: 深入解析FlowAI代码执行器节点的使用方法，掌握字符串处理、条件判断、循环操作等核心功能。通过实际案例学习如何实现数据转换、字符串处理、复杂逻辑处理等常见场景。
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI代码执行器, JavaScript数据处理, 工作流自动化, 数据转换技巧, 条件判断逻辑, 循环操作实例, 沙箱环境编程, 工作流调试方法"
    - tag: title
      content: "FlowAI 代码执行器完全指南：从基础到高级应用 | FlowAI 文档"
---

“代码执行器”是FlowAI中的一个强大组件，它允许用户编写和执行JavaScript代码来处理复杂的业务逻辑。通过代码执行器，你可以实现比如字符串处理、条件判断、循环操作等高级功能，极大地增强了工作流的灵活性和可扩展性。数据处理方面可以更加的随心所欲，不拘泥于我们目前提供的节点的功能。

:::note
重要提示：代码执行器运行在安全的沙箱环境（NodeJS 18）中，不支持网络请求，且执行时间限制为30秒（包括环境初始化时间）。请确保代码在此限制内完成，以避免执行失败。
:::

## 节点配置

### 基础设置

![代码执行器节点基础设置](./code-runner/base-setting.png)

1. **节点名称**

    - 为代码执行器设置一个描述性名称，以便于识别和管理。
    - 建议使用能体现处理目的的名称，如"数据转换"、"字符串处理"等，确保团队成员能够快速理解节点的功能。

2. **输入参数**
   支持两种参数输入方式：

    - **手动输入**：直接输入固定值，适用于简单场景。
    - **从上下文获取**：引用之前节点的输出值，适用于复杂的工作流。

3. **代码编辑区**
    - 在此区域编写JavaScript代码，确保代码逻辑清晰。
    - 必须使用`return`语句返回结果，以便后续节点能够获取到输出。
    - 可以通过`input.参数名`访问输入参数，灵活处理不同的输入。

## 代码格式规范

代码执行器会将你的代码封装在一个函数中执行，因此必须遵循以下格式：

```javascript
// 处理逻辑
var result = someOperation();
// 必须使用return返回结果
return result;
```

:::caution
注意事项：

- 必须使用`return`语句返回数据，否则下一个节点将无法获取结果。
- `console.log()`语句不会显示任何输出，因此调试时请使用其他方式捕获信息。
- 代码必须在30秒内完成执行，超时将导致执行失败。
  :::

## 使用示例

### 1. 基础示例 - 直接返回值

最简单的示例，直接返回一个值：

```javascript
return 1; // 返回数字1
```

### 2. 使用手动输入参数

```javascript
// 配置输入参数：
// 参数名：input1
// 参数值：123
// 输入方式：手动输入

return input.input1; // 将返回 "123"
```

### 3. 字符串处理示例

使用上下文参数进行字符串拼接：

```javascript
// 配置输入参数：
// 参数名：name
// 参数值：$开始.输入1
// 输入方式：从上下文获取

var output = "hello " + input.name; // 返回 "hello [name]"
return output;
```

### 4. 复杂逻辑处理

```javascript
// 示例：多重条件判断和数组处理
var data = input.data; // 获取输入数据
var result = [];

// 数组处理
for (var i = 0; i < data.length; i++) {
	if (data[i].value > 100) {
		result.push({
			id: data[i].id,
			status: "high", // 高值状态
		});
	} else if (data[i].value > 50) {
		result.push({
			id: data[i].id,
			status: "medium", // 中值状态
		});
	} else {
		result.push({
			id: data[i].id,
			status: "low", // 低值状态
		});
	}
}

return result; // 返回处理后的结果数组
```

### 5. 数据过滤与转换

```javascript
// 示例：过滤并转换商品数据
const products = input.products;

return products
	.filter((p) => p.price > 100) // 过滤高价商品
	.map((p) => ({
		id: p.id,
		name: p.name.toUpperCase(), // 标准化名称
		price: p.price * 0.9, // 应用9折
	}));
```

### 6. 类型转换处理

```javascript
// 示例：处理数字和字符串转换
const rawData = input.rawValue;

// 安全转换逻辑
return {
	stringValue: String(rawData).trim(),
	numberValue: isNaN(Number(rawData)) ? 0 : Number(rawData),
};
```

### 7. 数学运算示例

```javascript
// 示例：统计数据分析
const dataset = input.numbers;

return {
	average: dataset.reduce((a, b) => a + b) / dataset.length,
	maxValue: Math.max(...dataset),
	minValue: Math.min(...dataset),
};
```

## 最佳实践

1. **代码组织**

    - 保持代码简洁清晰，避免冗余。
    - 适当添加注释说明逻辑，帮助他人理解代码。
    - 将复杂逻辑分解为小的函数，提升可读性和可维护性。

2. **错误处理**

    ```javascript
    try {
    	// 你的业务逻辑
    	var result = doSomething(); // 执行某个操作
    	return result; // 返回结果
    } catch (error) {
    	return {
    		error: true,
    		message: error.message, // 返回错误信息
    	};
    }
    ```

3. **参数验证**
    ```javascript
    if (!input.required_param) {
    	return {
    		error: true,
    		message: "Missing required parameter", // 返回缺少参数的提示
    	};
    }
    ```

## 限制和注意事项

1. **执行环境限制**

    - 不支持网络请求，确保代码逻辑不依赖外部数据。
    - 不支持文件系统操作，避免文件读写相关的逻辑。
    - 执行时间限制为30秒，确保代码高效。

2. **代码安全**

    - 避免无限循环，确保代码能够正常终止。
    - 注意内存使用，避免内存泄漏。
    - 不要在代码中包含敏感信息，保护用户隐私。

3. **调试建议**

    - 在本地环境测试复杂逻辑，确保代码在真实环境中可用。
    - 使用try-catch捕获可能的错误，提升代码的健壮性。
    - 返回详细的错误信息便于调试，帮助快速定位问题。

4. **性能监控**
    - 使用`performance.now()`测量关键代码段的执行时间
    ```javascript
    const start = performance.now();
    // 关键逻辑
    const duration = performance.now() - start;
    return { result: data, duration };
    ```
