---
title: Code Runner Node
description: Dive deep into the usage of FlowAI's Code Runner node. Learn through practical examples how to use it to handle complex business logic.
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI Code Runner, JavaScript data processing, Workflow automation, Sandbox environment programming"
    - tag: title
      content: "FlowAI Code Runner Node Guide | FlowAI Documentation"
---

The Code Runner is a powerful component in FlowAI that enables users to write and execute JavaScript code for handling complex business logic. This tool empowers you to implement advanced functionalities such as string manipulation, conditional judgments, and loop operations, significantly enhancing workflow flexibility and extensibility. It provides unparalleled freedom in data processing, allowing you to go beyond the limitations of our existing node functionalities.

:::note
Important: The Code Runner operates in a secure sandbox environment (NodeJS 18) with network requests disabled and a 30-second execution time limit (including environment initialization). Please ensure your code completes within these constraints to prevent execution failures.
:::

## Node Configuration

### Basic Settings

![Code Runner Node Basic Settings](./code-runner/base-setting.png)

1. **Node Name**

    - Set a descriptive name for the code runner to facilitate identification and management.
    - Recommended to use purpose-reflective names like "Data Transformation" or "String Processing", ensuring team members can quickly understand the node's functionality.

2. **Input Parameters**
   Supports two input methods:

    - **Manual Input**: Directly enter fixed values, suitable for simple scenarios.
    - **Contextual Reference**: Retrieve values from previous node outputs, ideal for complex workflows.

3. **Code Editor**
    - Write JavaScript code in this area with clear logic.
    - Must include a `return` statement to pass results to subsequent nodes.
    - Access input parameters using `input.parameterName` (replace with actual parameter name) for flexible data handling.

## Code Format Specifications

The code runner will execute your code wrapped within a function. Follow this format:

```javascript
// Processing logic
var result = someOperation();
// Must return with return statement
return result;
```

:::caution
Important Notes:

- A `return` statement is mandatory for data output - subsequent nodes cannot receive results without it.
- `console.log()` statements will not produce any output. Use alternative debugging methods.
- Code must complete execution within 30 seconds - timeout will cause execution failure.
  :::

## Usage Examples

### 1. Basic Example - Direct Value Return

The simplest example directly returns a value:

```javascript
return 1; // Returns number 1
```

### 2. Using Manual Input Parameters

```javascript
// Configure input parameters:
// Parameter name: input1
// Parameter value: 123
// Input method: Manual input

return input.input1; // Will return "123"
```

### 3. String Processing Example

Using context parameters for string concatenation:

```javascript
// Configure input parameters:
// Parameter name: name
// Parameter value: $start.input1
// Input method: From context

var output = "hello " + input.name; // Returns "hello [name]"
return output;
```

### 4. Complex Logic Processing

```javascript
// Example: Multiple conditional judgments and array processing
var data = input.data; // Get input data
var result = [];

// Array processing
for (var i = 0; i < data.length; i++) {
	if (data[i].value > 100) {
		result.push({
			id: data[i].id,
			status: "high", // High value status
		});
	} else if (data[i].value > 50) {
		result.push({
			id: data[i].id,
			status: "medium", // Medium value status
		});
	} else {
		result.push({
			id: data[i].id,
			status: "low", // Low value status
		});
	}
}

return result; // Returns processed result array
```

### 5. Data Filtering & Transformation

```javascript
// Example: Filter and transform product data
const products = input.products;

return products
	.filter((p) => p.price > 100) // Filter high-priced items
	.map((p) => ({
		id: p.id,
		name: p.name.toUpperCase(), // Standardize name
		price: p.price * 0.9, // Apply 10% discount
	}));
```

### 6. Type Conversion Handling

```javascript
// Example: Handling number/string conversions
const rawData = input.rawValue;

// Safe conversion logic
return {
	stringValue: String(rawData).trim(),
	numberValue: isNaN(Number(rawData)) ? 0 : Number(rawData),
};
```

### 7. Mathematical Operations Example

```javascript
// Example: Statistical data analysis
const dataset = input.numbers;

return {
	average: dataset.reduce((a, b) => a + b) / dataset.length,
	maxValue: Math.max(...dataset),
	minValue: Math.min(...dataset),
};
```

## Best Practices

1. **Code Organization**

    - Keep code concise and clear, avoid冗余。
    - Add comments to explain the logic, helping others understand the code.
    - Decompose complex logic into smaller functions to improve readability and maintainability.

2. **Error Handling**

    ```javascript
    try {
    	// your business logic
    	var result = doSomething(); // execute some operation
    	return result; // return result
    } catch (error) {
    	return {
    		error: true,
    		message: error.message, // return error message
    	};
    }
    ```

3. **Parameter Validation**
    ```javascript
    if (!input.required_param) {
    	return {
    		error: true,
    		message: "Missing required parameter", // missing required parameter
    	};
    }
    ```

## Limitations and Considerations

1. **Execution Environment Restrictions**

    - Network requests are prohibited - ensure your code doesn't rely on external data
    - File system operations disabled - avoid file read/write logic
    - 30-second execution timeout - optimize code efficiency

2. **Code Security**

    - Avoid infinite loops to ensure proper termination
    - Monitor memory usage to prevent leaks
    - Never include sensitive data in code - protect user privacy

3. **Debugging Recommendations**

    - Test complex logic locally before deployment
    - Implement try-catch blocks to enhance error resilience
    - Provide detailed error messages to facilitate rapid troubleshooting

4. **Performance Monitoring**
    - Use `performance.now()` to measure execution time of critical code sections
    ```javascript
    const start = performance.now();
    // Critical logic
    const duration = performance.now() - start;
    return { result: data, duration };
    ```
