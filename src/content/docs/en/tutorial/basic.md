---
title: FlowAI Quick Start
description: Step-by-step tutorial to create AI workflows on FlowAI platform in 5 simple steps. Learn to connect LLM nodes, debug workflows, save and deploy workflows.
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI tutorial, Create AI workflow, LLM node usage, AI automation, GPT-4o integration"
    - tag: title
      content: "FlowAI Quick Start - Build Your First AI Workflow"
---

Quick Start: Create Your First AI Workflow

## 1. Log in to FlowAI Platform

1. Open your browser and visit [FlowAI Platform](https://flowai.cc)
2. Click the Login/Register button at top-right. Existing users can enter email/password. New users can sign up with GitHub or Google (e.g. click **Sign in with Google**).
   ![Login page](./basic/login.png)

## 2. Create Workflow from Scratch

### 2.1 Create New Workflow

First-time users will see a welcome page - click **Create Workflow**.
![Create workflow](./basic/create-workflow.png)
Existing users can click **New Workflow** from any page in the **FlowAI Dashboard**.

### 2.2 Workflow Panel

The workflow editor interface contains:

- 1: **Input Node** - Mandatory starting point (cannot be deleted)
- 2: Canvas controls (zoom, lock, etc.)
- 3: Node map overview
- 4: Save/Debug buttons
- 5: Node addition button (available after most nodes)

![Workflow panel](./basic/workflow-panel.png)

### 2.3 Configure Input Node

The **Input Node** is workflow's entry point. Click it to edit properties:
![Edit node](./basic/edit-node.png)

Add a new input field `Username`:
![Input node edit](./basic/input-node-edit.png)

### 2.4 Add LLM Node

Click `+` after Input Node and select **LLM**:
![Add LLM node](./basic/add-llm-node.png)

Configure the LLM Node:

- Select `GPT-4o`
- Set Prompt: `I'm $Start.Username, say hello to me`
  ![LLM node edit](./basic/llm-node-edit.png)

:::note
Use `$` symbol to insert variables. See [variable syntax](/tutorial/variable/).
:::

### 2.5 Add Output Node

Add **Output Node** after LLM Node. Set output to `LLM1.result`:
![Output node setting](./basic/output-node-setting.png)

## 3. Test & Debug Workflow

Click **Debug** button and enter test values (e.g. Username: `Jack`):
![Debug workflow](./basic/debug-workflow.png)

And click **Start Execution** button to see the result:
![Debug result](./basic/debug-result.png)

## 4. Save & Deploy

Click **Save** to store your workflow:
![Save workflow](./basic/save-workflow.png)

## 5. Manage Workflows

And then you can access saved workflows in the **Workflow List**:
![Workflow list](./basic/workflow-list.png)

If you want to run the workflow without editing, you can click **Run** button.

## FAQ

### Q1: How to choose LLM model?

A: GPT-4o for general use. Consider cost-effectiveness - DeepSeek models offer similar performance at lower cost.

### Q2: Can I share workflows?

A: Not currently supported (coming soon).

### Q3: How to monitor workflows?

A: Check **Logs** in dashboard. Every execution will create a log(but debug mode will not).

## Pro Tips

- Use variable templates for dynamic content
- Configure conditional branching
- Set up scheduled automation
