---
title: LLM Agent Node
description: A comprehensive guide to LLM Agent node configuration, usage methods, and best practices for maximizing the automation capabilities of intelligent agents
head:
    - tag: title
      content: "FlowAI LLM Agent Node - Intelligent Agent Workflow Integration and Automation | FlowAI Documentation"
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI LLM Agent Node, Intelligent Agent, Agent, GPT, Tool Calling, Automation, FlowAI, Workflow"
---

The LLM Agent node is a sophisticated component within the **FlowAI Smart Workflow** ecosystem, distinguishing itself from conventional LLM nodes. As an **intelligent agent**, the LLM Agent possesses the capability to **autonomously invoke tools** for executing complex tasks, thereby achieving advanced automated processing capabilities.

## LLM Agent Node Basic Configuration

### Node Properties Explained

![Node Properties](./llm-agent/llm-agent-node-setting.png)

1. **Node Name**: A customizable identifier for the LLM Agent node. Modifying this name will affect how other nodes in the workflow reference it. For instance, changing "LLMAgent1" to "Intelligent Assistant" will alter references from `$LLMAgent1.result` to `$Intelligent Assistant.result`. It is advisable to use descriptive names to enhance understanding and facilitate maintenance.

2. **Model**: Select the desired language model from the dropdown menu. Currently supported options include:

    - **GPT-4.1**: The latest high-performance model, offering full Agent capabilities (**Now fully supported**)
    - GPT-4o: Strikes an optimal balance between performance and cost
    - GPT-4o-mini: Offers a balanced approach to performance and cost
    - DeepSeek: The most economical and cost-effective model
    - Other models: Available for selection based on specific requirements

3. **Prompt Editing Area**: This section is used to compose instructions for the intelligent agent. You can:

    - Utilize variable syntax to reference outputs from other nodes
    - Craft detailed task descriptions
    - Specify expected output formats

4. **Output Language**: Define the Agent's response language, with support for multiple options including Chinese, English, and others. (Note that the output quality is contingent upon the LLM's level of intelligence.)

5. **Tools**: A suite of tools accessible to the Agent, currently including:

    - Web Scraping: Automates the retrieval of web content
    - Calculator: Performs mathematical computations
    - HTTP Request: Sends API requests to fetch data

6. **Maximum Iteration Rounds**: Specifies the maximum number of tool calls the Agent can execute, ranging from 1 to 50.

7. **MCP Server**: By configuring MCP (Model Context Protocol) servers, you can extend the Agent's capabilities, allowing the Agent to call external services and tools through the MCP protocol.

## Distinguishing LLM Agent from LLM Nodes

| Feature               | LLM Agent Node                                            | LLM Node                                        |
| --------------------- | --------------------------------------------------------- | ----------------------------------------------- |
| Functionality         | Intelligent agent capable of decision-making and tool use | Single AI inference for text content generation |
| Tool Calling          | Supports autonomous invocation of multiple tools          | Does not support tool calling                   |
| Task Complexity       | Ideal for complex, multi-step tasks                       | Suited for single text generation tasks         |
| Autonomy              | High, with ability to select tools based on task demands  | Low, adhering strictly to provided prompts      |
| Application Scenarios | Data collection, analysis, and automated processing       | Content generation and text analysis            |

## Prompt Writing Techniques

In contrast to LLM nodes, prompts for LLM Agent nodes emphasize **task description** and **goal definition**, rather than detailed execution steps.

### Basic Syntax

```
Please search for the latest information on "$input.keyword" at http://xxxx.com and provide a concise summary.
```

### Multi-step Task Example

```
Please execute the following tasks:
1. Retrieve the stock price for "$input.stock_code"
2. Calculate the average price over the past 7 days
3. If the average price exceeds $input.threshold, dispatch an HTTP request to our notification system

Note:
The stock query method is as follows:
1. Access https://xxxx.com/stock/xxxx
2. Replace xxx with the actual stock code
```

### Data Processing Example

```
Please analyze the following dataset: $input.data_table
Requirements:
1. Compute the mean value for each column
2. Identify outliers (data points exceeding two standard deviations)
3. Generate a comprehensive analysis report, including key findings and recommendations
```

## Detailed Tool Usage

The LLM Agent node can leverage multiple tools, each with specific use cases and capabilities:

### Web Scraping Tool

The web scraping tool empowers the Agent to automatically extract web content, suitable for:

- Gathering the latest news or information
- Obtaining product prices or specifications
- Extracting structured data from web pages

**Example Prompt**:

```
Please scrape the webpage at "$input.url", extract all product names and prices, and organize the information into a Markdown table format.
```

### Calculator Tool

The calculator tool enables the Agent to perform mathematical operations, ideal for:

- Data analysis and statistical computations
- Financial calculations
- Scientific calculations and unit conversions

Currently, the calculation tool supports basic mathematical syntax, including arithmetic operations, trigonometric functions, Pi, exponents, and more.

**Example Prompt**:

```
Please calculate the following statistical metrics for this dataset: $input.number_list
Required calculations: mean, median, standard deviation, and quartiles
```

### HTTP Request Tool

The HTTP request tool allows the Agent to interact with external APIs, suitable for:

- Data synchronization and integration
- Triggering operations in external systems
- Retrieving data from third-party services

**Example Prompt**:

```
Please transmit the following processing result: $input.processing_result
via an HTTP POST request to our API endpoint: $input.API_address
Ensure that the correct JSON format is utilized.
```

## Advanced Configuration Details

### Maximum Iteration Rounds Setting

The maximum iteration rounds parameter controls the number of tool calls the Agent can execute. However, it's not a strict one-to-one correspondence, as it depends on the AI's judgment. For certain parallel tasks, multiple tools may be called within a single iteration round.

1. **Parameter Specifications**:

    - Range: 1-50
    - Default value: Typically 3-5

2. **Setting Recommendations**:

    - Simple tasks: 3-5 rounds
    - Complex tasks: 10-15 rounds
    - Special scenarios: Adjust as needed

3. **Considerations**:
    - Excessive rounds may increase execution time and potentially cause the LLM's context to exceed the AI's supported context size
    - Insufficient rounds may result in incomplete task execution
    - It is advisable to assess your specific task requirements and select an appropriate number of rounds

### MCP Server Tools

MCP (Model Context Protocol) server tools are a new feature of the LLM Agent node, allowing the Agent to call external services and tools through a standardized protocol, greatly expanding the Agent's capability boundaries.

#### MCP Server Configuration

![MCP Server Configuration](./llm-agent/mcp-server-config.png)

**Configuration Steps**:

1. **Add MCP Server**: Click the "Add MCP Server" button
2. **Server Name**: Set a descriptive name for the MCP server; a good name helps the AI understand better
3. **Server Address**: Enter the URL address of the MCP server
4. **Server Type**: Select the appropriate connection type:
    - **SSE**: Server-Sent Events, suitable for real-time data streams
    - **Stream HTTP**: Streaming HTTP connection, suitable for most MCP services
5. **Custom HTTP Headers**: If needed, you can add authentication or other custom headers

#### MCP Server Usage Examples

**Basic Configuration Example**:

```
Server Name: Fetch Service
Server Address: https://remote.mcpservers.org/fetch/mcp
Server Type: Stream HTTP
```

**Prompt Example**:

```
Tell me about today's first trending project on GitHub
```

**Execution Process**:

1. After receiving the task, the Agent automatically identifies the need to get GitHub trending project information
2. Calls the configured fetch MCP service to scrape the GitHub Trends page
3. Parses the page content and extracts information about the first trending project
4. Returns detailed information including project name, description, star count, etc.

#### Advantages of MCP Servers

- **Standardized Protocol**: Based on the unified MCP protocol, ensuring compatibility
- **Strong Extensibility**: Can connect to various external services and APIs
- **Flexible Configuration**: Supports multiple connection types and custom configurations
- **Secure and Reliable**: Supports authentication and encrypted transmission

## Node Output Utilization

The output of the LLM Agent node is accessed via `$LLMAgentNodeName.result`, for example: `$LLMAgent1.result`

The output typically encompasses:

- The Agent's reasoning process
- Records of tool calls
- Final conclusions or processing results

## Common Application Scenarios

- **Data Collection and Analysis**: Automatically gather data from multiple websites, perform calculations and analysis, and generate comprehensive reports
- **Automated Customer Service**: Autonomously query knowledge bases, calculate prices, or dispatch notifications based on customer inquiries
- **Content Aggregation and Processing**: Collect information from diverse sources, integrate and analyze to produce summaries or reports
- **Intelligent Monitoring System**: Regularly inspect data sources, perform calculations and assessments, and trigger appropriate actions
- **Automated Workflow**: Receive input data, invoke multiple tools for processing, and output structured results
- **MCP Service Integration**: Connect to various external tools and services through MCP servers to achieve more powerful automation capabilities

### MCP Server Application Scenarios

By configuring different MCP servers, LLM Agent can achieve more specialized functions:

1. **Web Content Scraping**:

    - Configure fetch MCP service to automatically scrape web content
    - Real-time retrieval of news, prices, data, and other information
    - Support for complex web parsing and data extraction

2. **API Service Calls**:

    - Connect to various third-party API services
    - Automatically handle authentication and data format conversion
    - Implement cross-platform data synchronization

3. **Professional Tool Integration**:
    - Connect to professional analysis tools
    - Call domain-specific computing services
    - Extend the Agent's professional capabilities, such as enabling AI Agent to deploy websites, check PayPal information, etc.

## Usage Recommendations and Best Practices

1. **Task Definition**:

    - Clearly articulate task objectives and expected outputs
    - Decompose complex tasks into manageable steps
    - Provide comprehensive context information

2. **Tool Selection**:

    - Enable only the tools necessary for the task (as additional tools consume more tokens)
    - Consider the sequence and dependencies of tool calls
    - Provide clear usage guidelines for each tool

3. **Performance Optimization**:

    - Set an appropriate maximum iteration rounds
    - Avoid overly broad or ambiguous instructions
    - Provide well-structured input data

4. **Debugging Techniques**:

    - Initiate testing with simple tasks
    - Monitor the Agent's tool calling process closely
    - Refine prompts based on execution outcomes
    - Document successful cases as templates for future reference
    - Test MCP server connections and responses

5. **MCP Server Best Practices**:

    - Choose reliable MCP service providers
    - Regularly check server status and availability
    - Set appropriate timeout and retry mechanisms
    - Monitor MCP service usage and costs

By effectively configuring and utilizing the LLM Agent node, you can achieve highly automated intelligent workflows, significantly enhancing efficiency in handling complex tasks. For more detailed information about variable syntax, please consult the [Variable Syntax Guide](/tutorial/variable/).

:::tip
When implementing the LLM Agent node, it is recommended to begin with simple tasks for testing purposes, familiarize yourself with the Agent's operational methodology, and then progressively increase complexity. Simultaneously, closely monitor the Agent's tool calling process to ensure it executes as intended.
:::
