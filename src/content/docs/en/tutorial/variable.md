---
title: FlowAI Prompt Variables Guide
description: Comprehensive guide to FlowAI Prompt variable syntax. Master variable definition and referencing techniques to enhance AI workflow efficiency.
head:
    - tag: title
      content: "FlowAI Prompt Variables Guide | FlowAI Documentation"
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI Prompt variables, prompt variables, workflow variables, AI automation, FlowAI tutorial"
---

First, let's clarify two key concepts:

1. **What is a Prompt?**
   For short, a prompt is the input text that directs the AI's output.
   Imagine conversing with a highly intelligent assistant. A prompt is like your instructions to this assistant. By carefully crafting your prompt, you guide the AI to produce desired responses. In AI systems, a prompt is the input text that directs the AI's output.

2. **What are Variables?**
   For short, variables are the blank spaces in a fill-in-the-blank question. When creating prompts, you can insert these "blanks" (variables) that get populated with actual values during execution. This allows you to reuse the same prompt template while generating different outcomes by changing variable values.

## Variable Syntax Explained

### Variable Definition & Usage

FlowAI uses the `$` symbol to denote variables. This simple syntax is designed for intuitive recognition and ease of use.

The standard variable structure is:

```
$nodeName.output
```

Example for an LLM node's output:

```
$LLM1.result
```

Where:

- `LLM1`: Node name
- `result`: Standard output field

Each node may have different output fields. Check the **Node Details Panel** for specific output fields.
![Node output example](./variable/node-output.png)

### Variable Referencing Techniques

In FlowAI workflows, downstream nodes can directly reference upstream node outputs, enabling data transfer and automated processing.

Common use cases:

1. Using LLM1's output as input for LLM2
2. Passing processed data to analysis nodes
3. Chaining multi-step AI tasks

![Upstream-downstream nodes](./variable/upstream-downstream.png)

Referencing steps:

1. Type `$` in the editor
2. Select desired upstream variables from auto-suggest list
3. System auto-generates complete variable reference

![Variable reference diagram](./variable/downstream-node.png)
