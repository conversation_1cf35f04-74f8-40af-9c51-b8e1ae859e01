---
title: LLM Intent Classification Node
description: Master FlowAI's LLM Intent Node for intelligent content processing. Configure intent classification, sentiment analysis, and user intent recognition.
head:
    - tag: meta
      attrs:
          name: keywords
          content: "LLM, FlowAI LLM Content Classification, Sentiment Analysis, Intent Recognition, Workflow Control"
    - tag: title
      content: "FlowAI Intent Classification Node | FlowAI Documentation"
---

The LLM Intent Classification Node is one of the core features of the FlowAI platform, combining the intelligent understanding capabilities of Large Language Models (LLM) with conditional branching control. Through this node, you can achieve:

- Intelligent content classification
- Sentiment analysis
- User intent recognition
- Multi-dimensional content moderation

## Node Configuration Guide

### Basic Settings

![Basic Settings](./llm-intent/base-setting.png)

1. **Node Naming Conventions**

    - Use descriptive names
    - Examples: Content Moderation, Sentiment Analysis, User Intent Recognition

2. **Model Selection Recommendations**

    - Recommend using high-performance models like GPT-4o
    - Choose appropriate model versions based on business requirements
    - Can also select other models from a cost perspective, such as DeepSeek which offers good value for performance

3. **Classification Category Design**

    - Number of categories: 2-5
    - Ensure categories are mutually exclusive and comprehensive
    - Example: Positive/Negative/Neutral

4. **Prompt Optimization Tips**
    - Include clear classification criteria
    - Use structured instructions
    - Provide example references

## Usage Examples

### 1. Content Friendliness Detection

An example of detecting whether user input content is friendly:

1. **Input Node Configuration**

    ```
    Node Name: Start
    Variable Name: content
    Type: text
    ```

    ![Start Node](./llm-intent/user-input.png)

2. **LLM Intent Classification Configuration**

    ```
    Node Name: FriendlinessDetection
    Model: GPT-4o
    Classification Prompt: You are a professional content moderator, need to determine if the user input content is friendly.
    Categories:
    - Friendly Content
    - Unfriendly Content
    - Neutral Content

    User Input: $Start.content
    ```

    ![Content Friendliness Detection](./llm-intent/friendly-check.png)

3. **Branch Processing**
    - Friendly Content Branch: Return positive response
    - Unfriendly Content Branch: Give gentle reminder
    - Neutral Content Branch: Continue dialogue

### 2. Customer Service Intent Recognition

Identifying specific intent of customer inquiries:

```
Node Name: IntentRecognition
Model: GPT-4o
Separation Prompt: You are a professional customer service intent analyst, please determine which type of inquiry the user input belongs to
Categories:
- Order Query
- Complaint Feedback
- Refund Related
- Other Inquiries

User Input: $Start.content
```

![Customer Service Intent Recognition](./llm-intent/customer-service.png)

## Best Practices

1. **Prompt Design**

    - Provide clear classification criteria
    - Avoid ambiguous classification boundaries

2. **Classification Optimization**

    - Control the number of categories, avoid over-complexity
    - Ensure categories are mutually exclusive
    - Add "Other" category to handle edge cases, ensuring workflow continuity

## Advanced Usage

### 1. Multi-level Classification

Implement more refined classification by connecting multiple LLM intent classification nodes:

```
[Coarse Classification] --> [Fine Classification] --> [Final Processing]
```

### 2. Combined Analysis

Combine classification results from multiple dimensions:

```
Intent Classification: Inquiry/Complaint/Suggestion
Sentiment Classification: Positive/Negative/Neutral
Urgency Classification: Normal/Urgent/Critical
```

## Common Issues

:::caution
Common errors and optimization suggestions:

1. **Classification Design Optimization**

    ```
    ❌ Wrong Example:
    - Good/Bad
    - Yes/No

    ✅ Correct Example:
    - Positive and optimistic content
    - Negative and pessimistic content
    - Neutral or objective content
    ```

2. **Prompt Optimization**

    ```
    ❌ Wrong Example:
    "Judge this content"

    ✅ Correct Example:
    "Analyze the sentiment of the following content, return only one of POSITIVE/NEGATIVE/NEUTRAL"
    ```

3. **Edge Case Handling**
   `    ❌ Wrong: Not handling uncertain cases
✅ Correct: Adding UNCERTAIN or OTHER category`
   :::

## Debugging Tips

1. **Test Data Validation**

    - Prepare typical test cases to fully utilize few-shot capabilities
    - Include edge case testing
    - Record classification accuracy, continuously optimize prompts

2. **Classification Result Analysis**
    - Use output nodes to record results
    - Analyze reasons for misclassification
    - Continuously optimize prompts

Proper use of intent classification nodes can help workflows better adapt to different scenarios. Adjust classification rules according to specific business needs to make classification results more accurate and useful.
