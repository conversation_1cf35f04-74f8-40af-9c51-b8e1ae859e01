---
title: HTTP Request Node
description: A guide to using the FlowAI HTTP Request Node, mastering GET, POST methods, and learning core features. Learn common scenarios through practical examples.
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI HTTP Request, API call, Network Request Node, GET Request, POST Request"
    - tag: title
      content: "FlowAI HTTP Request Node | FlowAI Documentation"
---

The HTTP Request Node in FlowAI is a fundamental component used for sending network requests. It supports common HTTP methods (GET, POST, PUT, DELETE, etc.) and allows easy interaction with various Web APIs.

## Node Configuration

### Basic Settings

1. **Node Name**

    - Set a descriptive name, such as "Get Weather" or "Send Data."
    - Other nodes can reference its result through this name.
    - It is recommended to use a name that reflects the function for easier maintenance.

2. **HTTP Address**
   Two configuration options:

    - **From Variable**: Obtain the URL from another node (using this mode, you need to connect this node after others).
    - **Direct Input**: Manually enter a fixed URL.

3. **Request Method**
   Supports standard HTTP methods:

    - GET: Retrieve Data
    - POST: Create Data
    - PUT: Update Data
    - DELETE: Delete Data

4. **Request Headers**
    - You can add custom request headers.
    - Often used for authentication or specifying content type.
    - Format as key-value pairs.

## Node Output

The node outputs two main items:

- `$HTTPNodeName.body`: The response body content.
- `$HTTPNodeName.status`: The HTTP status code.

## Usage Examples

### Get UUID Example

Let's retrieve a UUID by calling httpbin.org's API:
![Get UUID Example](./http/get-uuid.png)

1. **Node Configuration**

    ```
    HTTP Address: https://httpbin.org/uuid
    Request Method: GET
    Request Headers: None
    ```

2. **Expected Output**

    ```json
    // Content of $HttpCall1.body
    {
    	"uuid": "2753d66b-6e6f-4667-8a46-27c4c4f915f6"
    }
    ```

3. **Status Code**
    ```
    // Content of $HttpCall1.status
    200
    ```

### POST Data Example

In this example, we send some data by calling httpbin.org's POST API:
![POST Data Example](./http/post-data.png)

1. **Node Configuration**

    ```
    HTTP Address: https://httpbin.org/post
    Request Method: POST
    Request Headers:
    key: Content-Type
    value: application/json

    Request Body:
    {
      "name": "Zhang San",
      "age": 25,
      "message": "$InputNode.message"
    }
    ```

2. **Expected Output**

    ```json
    // Content of $HttpCall1.body
    {
    	"args": {},
    	"data": "{\"name\":\"Zhang San\",\"age\":25,\"message\":\"Hello World\"}",
    	"files": {},
    	"form": {},
    	"headers": {
    		"Content-Type": "application/json",
    		"Content-Length": "54",
    		"Host": "httpbin.org"
    	},
    	"json": {
    		"name": "Zhang San",
    		"age": 25,
    		"message": "Hello World"
    	},
    	"url": "https://httpbin.org/post"
    }
    ```

3. **Status Code**
    ```
    // Content of $HttpCall1.status
    200
    ```

In this example, we:

- Selected the POST method, which enables the "Request Body" input box.
- Set the Content-Type header to application/json.
- Used the variable reference `$InputNode.message`, which will be replaced by the actual input value.

### Generic API Calling Process

```
[HTTP Request] --------> [JSON Processing] --------> [Data Display]
    |                             |                            |
    |                             |                            |
  Fetch raw data           Parse JSON data          Display processed result
```

## Precautions

:::tip
Usage Suggestions:

- Check if the URL format is correct.
- Ensure the appropriate request method is selected.
- Set the request headers appropriately.
  :::

:::caution
Common Issues:

- URL unreachable: Check your network connection and the accuracy of the address.
- Authentication failure: Verify that the authentication information is correct.
- Response format error: Refer to the API documentation for requirements.
  :::

## Best Practices

1. **Error Handling**

    - Check the status code to confirm if the request was successful.
    - Handle exceptions appropriately.

2. **Data Processing**
    - Use JSON Node to process structured data.
    - Utilize the LLM Node to analyze unstructured responses.
