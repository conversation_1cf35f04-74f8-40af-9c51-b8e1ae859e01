---
title: JSON Parameter Extraction Node
description: Learn to use FlowAI's JSON Extraction Node to extract data from JSON strings. Configure data sources and paths, and resolve extraction issues.
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI JSON Parameter Extraction, JSON Deserialization, JSON Path Extraction"
    - tag: title
      content: "FlowAI JSON Parameter Extraction Node | FlowAI Documentation"
---

The JSON Parameter Extraction Node is a component in FlowAI used for processing JSON data. It can extract specific data from JSON strings output by the previous node and pass it as variables to subsequent nodes. By properly using the JSON Parameter Extraction Node, you can easily handle complex JSON data structures.

:::note
Important Note: The JSON Parameter Extraction Node requires that the output from the previous node must be a valid JSON string. If the previous node is a "Content Concatenator" or "Input Node", ensure that the output content can be parsed by the JSON parser. If the previous node is an LLM, the LLM's "JSON Output" feature must be enabled to ensure the entire response is a valid JSON string.
:::

## Node Configuration

### Basic Settings

![JSON Parameter Extraction Node Basic Settings](./json-extract/base-setting.png)

1. **Node Name**

    - Set a descriptive name for the JSON Parameter Extraction Node
    - Recommend using names that reflect the extraction purpose, such as "Extract User Info", "Get Order Details", etc.
    - Naming convention: Use verb + noun form, like "Extract User ID", "Get Order Status"

2. **JSON Data Source**

    - Select or input the output variable from the previous node, such as `$LLM1.result`
    - Ensure the data source is a valid JSON string

3. **JSON Path**

    - Input the JSON path to extract, such as `output.data`, `output.list.1`, etc.
    - Path format: Use dot notation `.` for hierarchy, use numbers for array indices (starting from 0)

## Output

- Output variable format is `$NodeName.output`, for example `$JSONExtract1.output`

## Usage Examples

### 1. Extracting Simple JSON Data

Let's extract data from a simple JSON string:

1. A simple JSON string

    ```json
    {
    	"output": "Hello, World!"
    }
    ```

2. **JSON Parameter Extraction Configuration**

    ```
    Node Name: JSONParamExtraction1
    JSON Data Source: $input.data
    JSON Path: output
    ```

    ![JSON Parameter Extraction Configuration](./json-extract/simple-extract-node.png)

3. **Output**

    Use the extracted data for subsequent processing
    ![Output Node Configuration](./json-extract/simple-output-node.png)

### 2. Extracting Nested JSON Data

Handling nested JSON data structures:

1. A nested JSON string

    ```json
    {
    	"output": {
    		"advanceList": [{ "data": "Hello, World!" }]
    	}
    }
    ```

2. **JSON Parameter Extraction Configuration**

    ```
    Node Name: ExtractNestedData
    JSON Data Source: $input.data
    JSON Path: output.advanceList.0.data
    ```

    ![Nested JSON Parameter Extraction Configuration](./json-extract/nested-extract-node.png)

3. **Output**

    ![Output Node Configuration](./json-extract/nested-output-node.png)

### 3. Extracting Elements from Arrays

Demonstrating how to extract specific elements from JSON arrays:

1. A JSON array

    ```json
    {
    	"output": {
    		"list": ["Hello", "World", "FlowAI"]
    	}
    }
    ```

2. **JSON Parameter Extraction Configuration**

    ```
    Node Name: ExtractArrayElement
    JSON Data Source: $input.data
    JSON Path: output.list.1
    ```

    ![Array Element Extraction Configuration](./json-extract/array-extract-node.png)

3. **Output**

    ![Output Node Configuration](./json-extract/array-output-node.png)

## Advanced Usage

### 1. Multi-level Extraction

You can achieve multi-level data extraction by connecting multiple JSON Parameter Extraction Nodes, suitable for complex JSON data structures:

```
[Level 1 Extraction] --> [Level 2 Extraction] --> [Level 3 Extraction]
```

## Syntax Guidelines

The JSON Parameter Extraction Node uses GJSON path syntax, supporting rich query and operation features. Here's the complete syntax explanation:

### Basic Paths

- Use dot notation `.` for hierarchy
- Array indices start from 0

```json
{
	"user": {
		"tags": ["AI", "Developer", "GPT"]
	}
}
```

```
user.tags.1  // Extracts "Developer"
```

### Wildcards

- `*` matches any length of characters
- `?` matches a single character

```
user.t*      // Matches tags array
user.ta?s    // Matches tags array
```

### Escape Characters

Use `\` to escape special characters:

```json
{
	"file.name": "config.json"
}
```

```
file\.name  // Extracts "config.json"
```

### Array Operations

- `#` gets array length
- `#(query condition)` array query

```json
{
	"users": [
		{ "name": "Alice", "age": 25 },
		{ "name": "Bob", "age": 30 }
	]
}
```

```
users.#            // Returns 2 (array length)
users.#.name       // Returns ["Alice","Bob"]
users.#(age>25).name  // Returns "Bob"
```

### Query Syntax

Supports various query operators:

```json
{
	"products": [
		{ "id": 1, "price": 99, "tags": ["electronics", "sale"] },
		{ "id": 2, "price": 199, "tags": ["furniture"] }
	]
}
```

```
// Comparison queries
products.#(price>=100).id    // Returns 2
// Pattern matching
products.#(tags%"*sale*").id // Returns 1
// Nested queries
products.#(tags.#(=="furniture"))#.id // Returns 2
```

### Modifiers

Use data processing modifiers with the `@` symbol:

```
products.@reverse    // Reverse array order
products.@pretty     // Beautify JSON output
products.@ugly       // Compress JSON format
products.@join       // Merge multiple objects
```

:::tip
Best Practices

1. Complex paths should be extracted in steps using multiple connected nodes
2. Debug uncertain paths using output nodes first
3. Prefer index positioning for array queries (when structure is stable)
4. Always use escape syntax for field names containing special characters
   :::

## Common Issues

:::caution
Common Errors:

1. **Path Errors**

    ```
    JSON Path: output.data  // ❌ Error: Path doesn't exist
    ```

    Correct approach:

    ```
    JSON Path: output.data  // ✅ Correct: Ensure path exists
    ```

2. **Invalid Data Source**

    ```
    JSON Data Source: $LLM1.result  // ❌ Error: Data source is not a valid JSON string
    ```

    Solution: Ensure the data source is a valid JSON string, such as requiring LLM to output JSON string (enable LLM's JSON output feature).

3. **Array Index Out of Bounds**

    ```
    JSON Path: output.list.5  // ❌ Error: Array index out of bounds
    ```

    Solution: Ensure array index is within valid range

4. **Performance Optimization**
    - Avoid excessive nested extractions

:::

## Debugging Tips

**Using Output Nodes**
Add output nodes to each branch to help debug the flow:

```
[JSON Parameter Extraction] --> [Output Node(print extraction result)]
```

By properly using the JSON Parameter Extraction Node, you can easily handle complex JSON data structures. Remember to carefully plan extraction paths and ensure path accuracy and validity to guarantee reliable workflow operation.
