---
title: Advanced Template Node
description: Explore FlowAI's Advanced Template Node for dynamic content rendering, featuring built-in functions and versatile use cases for efficient text processing.
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI Tutorial, AI Workflow Creation, LLM Node Usage, AI Automation, Go Template"
    - tag: title
      content: "FlowAI Advanced Template Node - FlowAI Documentation"
---

The Advanced Template Node is a powerful feature in the FlowAI platform that allows users to dynamically render content based on template rules using **Go Template** syntax. Through this node, you can create complex text processing logic, implementing conditional rendering and formatting of content.

![Advanced Template Node](./advance-template/advance.png)

## Basic Usage

The Advanced Template Node follows the syntax rules of **Go Template**, enabling you to:

- Reference variables
- Use conditional statements
- Create loops
- Apply filters and functions
- Process JSON data

## Configuration Steps

1. **Node Naming**: Set a meaningful name for your template node, such as "AdvancedTemplate1". Make sure not to use special characters or spaces.
2. **Add Input Parameters**: Click the "+ Add" button to configure the required input variables
3. **Write Template Content**: Write your template using **Go Template** syntax in the content area

## Input Parameters

You can add multiple input parameters in the node configuration. These parameters will be used as variables in the template:

- Parameter names should be concise and clear, such as "input1"
- Parameter values can be text, numbers, or data passed from other nodes
- In the workflow, you can reference outputs from other nodes as inputs using formats like `$Start.input1`

## Go Template Syntax Examples

### Variable References

```
{{ .varName }}
```

Example: Using `{{ .input1 }}` references an input parameter named "input1"

### Conditional Statements

```
{{if .condition}}...{{else}}...{{ end }}
```

**Practical Example**: Generate different responses based on user mood

```
{{if eq .mood "happy"}}
  Glad to see you're in a good mood today! We recommend the following activities:
  - Outdoor picnic
  - Meeting with friends
  - Trying a new hobby
{{else if eq .mood "tired"}}
  Looks like you need some rest. Here are some relaxation suggestions:
  - Meditate for 15 minutes
  - Drink a cup of warm herbal tea
  - Go to bed early
{{else}}
  Thank you for your feedback! We're here to help anytime.
{{ end }}
```

### Loops

```
{{range .items}}...{{ end }}
```

**Practical Example**: Generate a to-do list

```
# Today's To-Do List

{{range .todoItems}}
## {{.title}}
- Priority: {{.priority}}
- Due Date: {{.dueDate}}
- Description: {{.description}}

{{end}}
```

### Pipeline Operations

```
{{ .value | function1 | function2 }}
```

**Practical Example**: Format user input

```
Original input: {{ .userInput }}
Formatted: {{ .userInput | trim | lower }}
```

## Built-in Functions

The Advanced Template Node provides a rich set of built-in functions to help you handle various data transformation and operation needs:

### JSON Processing Functions

| Function         | Description                      | Example                                                              |
| ---------------- | -------------------------------- | -------------------------------------------------------------------- |
| `json_parse`     | Parse JSON string into an object | `{{ $data := json_parse .jsonString }}{{ $data.name }}`              |
| `json_stringify` | Convert object to JSON string    | `{{ $obj := dict "name" "John" "age" 30 }}{{ json_stringify $obj }}` |

**Practical Example**: Processing JSON data returned from an API

```
{{$apiResponse := json_parse .apiResult}}

# User Profile

- Name: {{$apiResponse.user.name}}
- Email: {{$apiResponse.user.email}}
- Membership Level: {{$apiResponse.user.membershipLevel}}

## Recent Orders
{{range $apiResponse.user.orders}}
- Order ID: {{.id}} - Amount: ${{.amount}} - Status: {{.status}}
{{end}}
```

### String Processing Functions

| Function     | Description                  | Example                                                         |
| ------------ | ---------------------------- | --------------------------------------------------------------- |
| `upper`      | Convert to uppercase         | `{{ .text \| upper }}`                                          |
| `lower`      | Convert to lowercase         | `{{ .text \| lower }}`                                          |
| `trim`       | Remove spaces from both ends | `{{ .text \| trim }}`                                           |
| `trim_left`  | Remove spaces from the left  | `{{ .text \| trim_left }}`                                      |
| `replace`    | Replace string               | `{{ replace .text "old text" "new text" }}`                     |
| `split`      | Split string into array      | `{{ $arr := split .text "," }}`                                 |
| `join`       | Join array into string       | `{{ join .tags ", " }}`                                         |
| `contains`   | Check if contains substring  | `{{ if contains .text "keyword" }}Contains keyword{{ end }}`    |
| `has_prefix` | Check if starts with prefix  | `{{ if has_prefix .code "A" }}A-class product{{ end }}`         |
| `has_suffix` | Check if ends with suffix    | `{{ if has_suffix .email ".com" }}International email{{ end }}` |

**Practical Example**: Format customer feedback

```
{{$keywords := split .feedbackTags ","}}

# Customer Feedback Analysis

Original feedback: {{.feedback}}
Formatted feedback: {{replace (lower .feedback) "unsatisfied" "needs improvement"}}

## Key Tags
{{range $keywords}}
- {{. | trim | upper}}
{{end}}

## Sentiment Analysis
{{if contains (lower .feedback) "satisfied"}}
Positive feedback ✓
{{else if contains (lower .feedback) "disappointed"}}
Negative feedback ✗
{{else}}
Neutral feedback ○
{{end}}
```

### Mathematical Functions

| Function | Description    | Example                      |
| -------- | -------------- | ---------------------------- |
| `add`    | Addition       | `{{ add .num1 .num2 }}`      |
| `sub`    | Subtraction    | `{{ sub .total .discount }}` |
| `mul`    | Multiplication | `{{ mul .price .quantity }}` |
| `div`    | Division       | `{{ div .total .count }}`    |
| `mod`    | Modulus        | `{{ mod .number 2 }}`        |

**Practical Example**: Shopping cart calculation

```
# Shopping List

{{range .items}}
- {{.name}}: ${{.price}} x {{.quantity}} = ${{mul .price .quantity}}
{{end}}

## Cost Summary
- Subtotal: ${{.subtotal}}
- Discount: ${{.discount}}
- Shipping: ${{.shipping}}

## Amount Due
Total: ${{add (sub .subtotal .discount) .shipping}}

{{if gt .discount 0}}
You saved: ${{.discount}}!
{{end}}
```

### Array/Slice Operation Functions

| Function | Description                | Example               |
| -------- | -------------------------- | --------------------- |
| `first`  | Get first element of array | `{{ first .items }}`  |
| `last`   | Get last element of array  | `{{ last .items }}`   |
| `length` | Get length                 | `{{ length .items }}` |

**Practical Example**: Processing survey results

```
{{$responses := json_parse .surveyResponses}}

# Survey Results Analysis

- Total responses: {{length $responses}}
- First respondent: {{(first $responses).name}}
- Last respondent: {{(last $responses).name}}

## Detailed Responses
{{range $index, $resp := $responses}}
{{add $index 1}}. {{$resp.name}} ({{$resp.age}} years old)
   Question 1: {{$resp.q1}}
   Question 2: {{$resp.q2}}

{{end}}
```

## Advanced Application Scenarios

### Scenario 1: Smart Customer Service Response Generation

```
{{$customerData := json_parse .customerInfo}}

Dear {{$customerData.name}}{{if eq $customerData.vipLevel "gold"}}, our esteemed Gold Member{{end}},

Thank you for your inquiry about "{{.issueTitle}}".

{{if contains (lower .issueDescription) "refund"}}
Regarding your refund request, we will process it within {{add 1 2}} business days. Your order number {{$customerData.lastOrder.id}} has been recorded.
{{else if contains (lower .issueDescription) "delivery"}}
Your package is in transit and expected to arrive on {{.estimatedDelivery}}.
Tracking number: {{$customerData.lastOrder.trackingNumber}}
{{else}}
We have received your feedback, and our customer service team will contact you shortly.
{{end}}

{{if gt (length $customerData.pendingOrders) 0}}
You currently have {{length $customerData.pendingOrders}} pending orders:
{{range $customerData.pendingOrders}}
- Order ID: {{.id}} | Status: {{.status}} | Amount: ${{.amount}}
{{end}}
{{end}}

Have a great day!
{{if has_suffix $customerData.name "Ms."}}Ms.{{else}}Mr.{{end}} {{$customerData.lastName}}
```

### Scenario 2: Data Report Generation

```
{{$salesData := json_parse .monthlySales}}
{{$totalSales := 0}}
{{$bestMonth := ""}}
{{$bestSales := 0}}

# Sales Report for {{.year}}

## Monthly Sales
{{range $month, $amount := $salesData}}
- {{$month}}: ${{$amount}}
{{$totalSales = add $totalSales $amount}}
{{if gt $amount $bestSales}}
  {{$bestSales = $amount}}
  {{$bestMonth = $month}}
{{end}}
{{end}}

## Sales Statistics
- Annual Total Sales: ${{$totalSales}}
- Monthly Average: ${{div $totalSales 12}}
- Best Performing Month: {{$bestMonth}} (${{$bestSales}})
- Year-over-Year Growth: {{if gt .growthRate 0}}↑{{else}}↓{{end}}{{.growthRate}}%

## Product Category Analysis
{{range .categories}}
### {{.name}}
- Sales: ${{.sales}} (Share: {{mul (div .sales $totalSales) 100}}%)
- Best-selling Product: {{.topProduct}}
{{end}}
```

### Scenario 3: Personalized Email Templates

```
{{$user := json_parse .userData}}

Subject: {{if eq .emailType "welcome"}}Welcome to {{.companyName}}{{else if eq .emailType "birthday"}}Happy Birthday, {{$user.name}}!{{else}}Latest News from {{.companyName}}{{end}}

{{if eq .emailType "welcome"}}
Dear {{$user.name}},

Welcome to the {{.companyName}} family! We're thrilled to have you on board.

Here's your account information:
- Username: {{$user.username}}
- Membership Level: {{$user.membershipLevel}}
- Registration Date: {{$user.registrationDate}}

{{else if eq .emailType "birthday"}}
Dear {{$user.name}},

Happy {{$user.age}}th Birthday!

To celebrate your special day, we've prepared a {{.birthdayOffer}} coupon for you, valid until {{.offerExpiry}}.

{{else}}
Dear {{$user.name}},

Thank you for your continued support of {{.companyName}}.

Here's our latest news:
{{range .newsItems}}
## {{.title}}
{{.content}}

{{end}}
{{end}}

Best regards,
The {{.companyName}} Team
```

## Using the Output

The output of the Advanced Template Node can be used in subsequent nodes in the workflow:

- Output reference format: `$AdvancedTemplate1.template`
- You can connect this output to other nodes as input
- The processed template result can be directly used for email sending, API requests, or other operation nodes

## Best Practices

1. **Modular Design**: Break complex templates into multiple smaller templates to improve maintainability
2. **Preprocess Data**: Use JSON processing nodes to organize data structures before templating
3. **Test and Validate**: Validate template outputs with test data to ensure correct formatting
4. **Add Comments**: Include comments in complex templates to improve readability
5. **Error Handling**: Use conditional statements to handle potential null values or exceptional cases

By effectively using the Advanced Template Node, you can significantly enhance your workflow automation capabilities, achieving more flexible and powerful content processing functions. Whether generating reports, personalizing communications, or transforming data, the Advanced Template Node can meet your needs.
