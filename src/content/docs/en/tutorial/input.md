---
title: Input Node
description: A concise review of the Input node's role in FlowAI, emphasizing variable settings, input type adjustments, and configuration for effective workflow control.
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI Input Node, Workflow Input Configuration, Variable Management, Workflow Development"
    - tag: title
      content: "FlowAI Input Node | FlowAI Documentation"
---

The Input node, serving as the first node in a workflow, plays a crucial role as the entry point and receiver of external inputs. It is automatically added when creating a workflow and cannot be deleted, ensuring a definitive starting point for data flow. This article will detail the various features and usage techniques of the Input node.

## Node Properties in Detail

![Node Property Editor](./input/input-node-setting.png)

1. **Node Name**: A customizable identifier for the input node. Modifying it affects how other nodes in the workflow reference this node. For example, changing "Start" to "Input1" will change references from `$Start.xxx` to `$Input1.xxx` in other nodes.

2. **Variable Editing Area**: Used for defining and managing key variables in the workflow, supporting various data type configurations.

3. **Add New Variable Button**: Quickly add new variables to the editing area, improving work efficiency.

## Variable Operation Area Details

### Basic Operations

![Variable Basic Operation Area](./input/input-node-variable-area-basic-operation.png)

### Type Modification

Variable types can be flexibly adjusted through the dropdown menu.

![Variable Type Modification](./input/input-node-variable-area-change-type.png)

### Order Adjustment

Drag-and-drop functionality allows free adjustment of variable order, optimizing the user input experience.

![Variable Drag](./input/input-node-variable-area-drag.png)

## Diverse Input Types

### Text/Long Text

Suitable for collecting user text information, supporting both short and long text styles to meet different input scenario needs.

![Text Input](./input/input-node-variable-area-text.png)

### Radio Button/Dropdown

Provides preset options for users, ensuring at least one option is available, suitable for fixed option selection scenarios.

- Supports adding and deleting options
- Can set default selection by clicking the "Default" button (limited to one)
- Provides clear option display and selection experience

![Radio/Dropdown](./input/input-node-variable-area-select.png)

Actual effect in the runner:

![Radio/Dropdown Runner](./input/input-node-variable-area-select-runner.png)

### Checkbox

Allows users to select multiple options, suitable for multi-keyword scenarios, supporting flexible multiple selections.

![Checkbox](./input/input-node-variable-area-checkbox.png)

Actual effect in the runner:

![Checkbox Runner](./input/input-node-variable-area-checkbox-runner.png)

:::note
In FlowAI, all inputs are ultimately stored as text. Multiple selection results are returned as comma-separated strings, such as `sci-fi,romance`, facilitating processing by subsequent nodes.
:::

## Using the Input Node

Variables configured in the Input node are rendered as corresponding input controls in the runner. Information entered by users is saved to these variables for use by other nodes in the workflow.

Variable reference syntax: `$nodeName.variableName`

For more detailed information about variable syntax, please refer to the [Variable Syntax Guide](/tutorial/variable/).

By properly setting up and using the Input node, you can establish a solid data foundation for the entire workflow, ensuring subsequent nodes can effectively process and transform these input data.

<a href="https://flowai.cc/dashboard/projects" target="_blank" rel="noopener noreferrer">Experience FlowAI now and start your AI workflow automation journey!</a>
