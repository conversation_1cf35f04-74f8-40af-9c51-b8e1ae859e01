---
title: JSON Pretty Node
description: This guide explains how to employ the JSON Pretty node in FlowAI to reformat messy JSON into an elegant, structured display for seamless downstream integration.
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI JSON Pretty, Data Processing Node, JSON Format Optimization, Workflow Automation"
    - tag: title
      content: "FlowAI JSON Pretty Node | FlowAI Documentation"
---

The JSON Pretty node is a functional component in FlowAI used to beautify and optimize JSON data display. By formatting the input JSON data, you can view and use this data more clearly in subsequent processes.

## Node Configuration

### Basic Settings

![JSON Pretty Node Basic Settings](./json-format/base-setting.png)

1. **Node Name**

    - Set a descriptive name for the JSON Pretty node
    - Recommended to use names that reflect the data processing purpose, such as "Format User Data", "Beautify Order Information", etc.
    - Naming convention: Use verb + noun format, such as "Format Response Data", "Beautify Log Content"

2. **JSON Data Source**

    - Specify the source of JSON data that needs formatting
    - Supports selecting appropriate variables from previous node outputs as data sources
    - For example: `start.data` indicates getting data from a node named "start"

3. **Output**

    - The formatted JSON data will be output as a new variable
    - For example: `$JSONPretty1.output` is used for reference in subsequent nodes

## Usage Examples

### 1. JSON Data Formatting

Learn how to format JSON data in FlowAI through the following steps:

1. **Select JSON Pretty Node**

    In the flow design interface, select "JSON Pretty" from the "Data Processing" section.

2. **Configure Node**

    ```
    Node Name: JSONPretty1
    JSON Data Source: Start.data
    Output: $JSONPretty1.output
    ```

    ![JSON Pretty Configuration](./json-format/base-setting.png)

3. **Connect to Content Combiner**

    Connect the formatted JSON data to the "Content Combiner" node for better display:

    ````
    Node Name: ContentCombiner1
    Content:
    ```json
    $JSONPretty1.output
    ```
    ````

    ![Content Combiner Configuration](./json-format/content-combiner-config.png)

4. **Output**

    ![Output Node Configuration](./json-format/test-output.png)

### 2. Combining with Other Processing Nodes

Formatted JSON data can be further processed in other nodes, such as parameter extraction or data analysis.

## Advanced Usage

### 1. Multi-level JSON Data Processing

When handling complex multi-level JSON data, the Pretty node can help you understand the data structure more intuitively, making it easier to extract specific fields or perform conditional judgments.

### 2. Combined Node Usage

You can combine the JSON Pretty node with other data processing nodes to achieve more complex workflows, for example:

```
[JSON Pretty] --> [JSON Parameter Extraction] --> [Conditional Logic] --> [Content Combiner]
```

## Best Practices

1. **Clear Formatting Purpose**

    - Ensure clear use cases for Pretty nodes, avoid unnecessary performance overhead
    - Use Pretty nodes when display or further processing is needed

2. **Variable Management**

    - Properly name output variables for easy reference in subsequent nodes
    - Ensure correct variable paths to avoid data source errors

3. **Debugging and Optimization**

    - Use content combiner nodes to output formatting results for debugging
    - Consider JSON data size and complexity to optimize processing flow

## Common Issues

:::caution
Common Errors:

1. **Data Source Error**

    ```
    Incorrect JSON data source entry leading to data retrieval failure
    ```

    Solution: Ensure correct data source path and required data output in previous nodes

2. **Undefined Output Variable**

    ```
    Variables referenced in subsequent nodes are not properly defined
    ```

    Solution: Check Pretty node output settings and ensure variable name consistency

3. **Performance Issues**

    ```
    Processing overly large or complex JSON data may affect performance
    ```

    Solution: Optimize JSON data structure or process in batches

:::

Through proper use of the JSON Pretty node, you can significantly improve the readability and efficiency of data processing. Ensure correct configuration of data sources and output variables in the workflow to achieve automated and precise data management.
