---
title: Template Node
description: Explore FlowAI's template node to merge multiple inputs into a single output, using content reuse and variable integration techniques.
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI Template, Workflow Content Integration, Content Reuse, Template Best Practices"
    - tag: title
      content: "FlowAI Template Node | FlowAI Documentation"
---

The template node is one of the core components in FlowAI, capable of integrating inputs from multiple nodes into a single output. Through proper use of template nodes, you can achieve content reuse and variable merging, thereby simplifying complex workflows.

:::note
Important Note: Template nodes support variable insertion and can merge content from multiple variables into a single output. This makes it particularly useful in scenarios requiring integration of multiple inputs.
:::

## Node Configuration

### Basic Settings

1. **Node Name**

    - Set a descriptive name for the template node
    - Recommend using names that reflect the merging purpose, such as "Merge User Info", "Integrate Order Data", etc.
    - Naming convention: Use verb + noun format, like "Merge User Info", "Integrate Order Data"

2. **Content Settings**

    - You can directly input text and insert variables in the content area
    - Variable format is `$variableName`, for example `$LLM1.result` or `$input.input`
    - Supports insertion of multiple variables and custom text between variables

## Output

    - The merged content will be output as a new variable
    - Output variable format is `$nodeName.template`, for example `$template1.template`

## Usage Examples

### 1. Merging User Information

Let's create a simple user information merging process, one of the most common scenarios in FlowAI:

1. **Input Node Configuration**

    ![User Info Input Node Configuration](./template/user-info-input-node.png)

2. **Template Configuration**

    ```
    Node Name: MergeUserInfo
    Content:
    User Name: $Start.name
    User Age: $Start.age
    User Address: $Start.address
    ```

    _**Note**: Ensure all referenced variables exist before the template node!_

    ![Template Configuration](./template/user-info-merge-node.png)

3. **Output Node Configuration**

    Process the merged content for subsequent operations
    ![Output Node Configuration](./template/user-info-output-node.png)

### 2. Integrating Order Data

A practical example - integrating multiple order information into one output, suitable for order processing, data analysis, and other scenarios:

1. **Input Node Configuration**
   ![Order Info Input Node Configuration](./template/order-info-input-node.png)

2. **Template Configuration**

    ```
    Node Name: IntegrateOrderData
    Content:
    Order Number: $Start.orderId
    Order Amount: $Start.amount
    Order Status: $Start.status
    ```

    ![Template Configuration](./template/order-info-merge-node.png)

3. **Processing Node Configuration**

    For example, you can:

    - Send integrated order data to database
    - Send integrated order data to email system
    - Send integrated order data to reporting system

### 3. Message Template Generation

Demonstrating how to use template nodes to generate message templates, suitable for message pushing, notification sending, and other scenarios:

```
Node Name: Generate Message Template
Content:
Dear $Start.name:
Your order $Start.orderId has been $Start.status.
Thank you for your support!
```

![Message Template Generation Configuration](./template/message-template-merge-node.png)

## Advanced Usage

### 1. Nested Templates

You can achieve complex logic by connecting multiple template nodes, suitable for multi-level content integration and complex business rules:

```
[User Info Template] --> [Order Info Template] --> [Message Template Generation]
```

### 2. Combined Template Example

Using multiple variables and custom text in one template node, suitable for scenarios requiring simultaneous integration of multiple variables:

```
Node Name: GenerateReport
Content:
Report Date: $Start.date
Total Users: $Start.userCount
Total Orders: $Start.orderCount
Total Sales: $Start.totalSales
```

## Best Practices

1. **Content Design Principles**

    - Pay attention to variable naming conventions
    - Ensure variables exist before the template node
    - Use comments to explain business meaning of each variable
    - Keep content logic simple and maintainable

2. **Variable Handling**

    - Verify variable existence before templating
    - Consider data type consistency
    - Mind variable scope

3. **Error Handling**
    - Add default value handling for exceptional cases
    - Add logging at critical template nodes
    - Consider necessity of data validation

## Common Issues

:::caution
Common Errors:

1. **Variable Issues**

    The user hasn't provided the name variable in the `Input Node`, but the template node needs to use these variables. Please ensure all variables exist and variable names are correct. **Especially after modifying node names!**

    ```
    Content:
    User Name: $input.name
    User Age: $input.age
    // ❌ Error: If $input.name doesn't exist, templating will fail, showing as `$input.name`
    ```

2. **Missing Variables**

    ```
    Content:
    User Name: $input.name
    User Age: $input.age
    // Missing user address
    ```

    Correct approach: Ensure all required variables are included

3. **Case Sensitive Variables**

    ```
    Content:
    User Name: $input.Name  // Won't match $input.name
    ```

    Solution: Pay attention to variable name case

4. **Performance Optimization**
    - Avoid excessive nested templating

:::

## Debugging Tips

**Using Output Nodes**
Add output nodes after each template node to help debug the flow:

```
[Template Node] --> [Output Node(Print Template Result)]
```

Through proper use of template nodes, you can build flexible and powerful workflows. Remember to carefully plan templating logic and ensure variable completeness and consistency to guarantee reliable workflow operation.
