---
title: Current Time Node
description: Learn how to use the Time Node in FlowAI to obtain system current time and provide precise temporal context for LLMs. Master time formats, use cases, and best practices.
head:
    - tag: title
      content: "FlowAI Current Time Node - Get System Time | FlowAI Documentation"
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI Time Node, system current time, temporal context, automation tools, LLM time processing, intelligent time management"
---

**Current Time Node** is a utility component in FlowAI that provides temporal context for LLMs. This AI workflow tool enables precise time acquisition down to the second, helping LLMs accurately understand temporal information in conversations and task processing. Whether for automation workflows, intelligent dialog systems, or data analysis, the Time Node delivers reliable temporal references for your AI applications.

## Node Configuration

![Time Node](./time/get-time.png)

### Basic Settings

1. **Node Name**

    - Set a descriptive name like "Current Time" or "System Time"
    - Other nodes can reference time results through this name
    - Recommended to use functional names for easier maintenance

## Node Output

The node outputs one primary element:

- `$TimeNodeName.time`: Current system time

**Time Format**

- Format: `YYYY-MM-DD HH:mm:ss`
- Example: `2025-01-02 03:04:05`

## Usage Examples

### Providing Temporal Context for LLMs

The primary purpose of the Time Node is to supply accurate time information for LLMs:

1. **Weather Query Scenario**

    ```
    User: What's the weather today?
    LLM: Today is 2025-01-02. Querying weather for today at 03:04:05...
    ```

2. **Data Query Scenario**

    ```
    User: Help me query this month's order data
    LLM: Current time is January 2, 2025. Querying order data for January 2025...
    ```

3. **Schedule Management Scenario**

    ```
    User: Remind me about tomorrow's afternoon meeting
    LLM: Current time is 2025-01-02 15:00:00. Meeting reminder set for 2025-01-03 15:00
    ```

## Important Notes

:::tip
Recommendations:

- Use meaningful names for time nodes
  :::

:::caution
Common Issues:

- Format mismatch: Verify time format requirements
  :::

## Best Practices

1. **LLM Context Support**

    - Provide accurate temporal references for LLMs
    - Support time-related reasoning and decision making

2. **Dynamic Query Generation**

    - Help LLMs generate time-based dynamic queries
    - e.g., SQL queries, API requests

3. **Time-Sensitive Tasks**

    - Support time-dependent task processing
    - e.g., schedule management, timed reminders

## FAQ

### What is the primary purpose of the Time Node?

The Time Node primarily provides accurate temporal information to help AI systems better understand time context in user requests.

### What is the output format of the Time Node?

The Time Node outputs standard `YYYY-MM-DD HH:mm:ss` format, e.g., `2025-01-02 03:04:05`.

### How to use the Time Node in FlowAI?

Simply add the Time Node to your workflow, set its name, then reference current time via `$NodeName.time` in other nodes.

## Typical Use Cases

### 1. Intelligent Customer Service

- Provide accurate temporal references
- Support time-related user queries
- Enable time-aware personalized responses

### 2. Data Analysis Workflows

- Timestamp data records
- Support time series analysis
- Enable dynamic data queries
