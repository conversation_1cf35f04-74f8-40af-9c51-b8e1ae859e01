---
title: Condition Node
description: In-depth explanation on the use of FlowAI condition nodes, mastering core features such as string matching. Learn how to implement common scenarios.
head:
    - tag: meta
      attrs:
          name: keywords
          content: "FlowAI Condition Node, Workflow Branch Control, String Matching, Process Control Node"
    - tag: title
      content: "FlowAI Condition Node | FlowAI Documentation"
---

The **condition node** is a core control component in FlowAI that allows you to split the workflow into different execution paths based on specified conditions. By using condition checks effectively, you can build complex business logic.

:::note
**Important Note:** The condition node treats all input as strings. This means that even if a number is provided, it will be converted to a string for comparison. As a result, numerical comparisons (such as greater than or less than) are not supported.
:::

## Node Configuration

### Basic Settings

![Basic Settings of the Condition Node](./condition/base-setting.png)

1. **Node Name**

    - Set a descriptive name for the condition node.
    - It is recommended to use a name that reflects the purpose of the check, such as "Check Age" or "Verify Permissions."
    - Naming convention: use a verb+noun format, e.g., "Verify User Permissions" or "Check Order Status."

2. **Condition Content**

    - Only variable references are supported; ensure that a variable has been output from a preceding node.

3. **Condition Options**

    The following string comparison methods are supported:

    - **Equal / Not Equal:** Exact string matching.
    - **Contains / Does Not Contain:** Checks if the string contains a substring; even a partial match is considered successful.
    - **Starts With / Does Not Start With:** Checks the prefix of the string.
    - **Ends With / Does Not End With:** Checks the suffix of the string.

## Usage Examples

### 1. User Type Determination

Let’s create a simple workflow to determine user type—one of the most common scenarios in FlowAI:

1. **Input Node Configuration**

    ![User Type Input Node Configuration](./condition/user-type-input-node.png)

2. **Condition Check Configuration**

    ```
    Node Name: User Type Check
    Condition Content: $input.userType
    Condition 1: Equal to "VIP"
    Condition 2: Equal to "Regular User"
    Condition 3: Contains "" // Using an empty string in the 'Contains' condition to represent all other cases; it will match anything not matched earlier.
    ```

    _**Note:** Conditions are evaluated in sequence, so Condition 3 must be placed last to avoid prematurely matching other cases!_

    ![Condition Check Configuration](./condition/user-type-condition-node.png)

3. **Output Node Configuration**

    Use different branches to handle different user types.

    ![Output Node Configuration](./condition/user-type-output-node.png)

### 2. Message Type Classification

A practical example—processing messages with different responses, suitable for chatbots, customer service systems, etc.:

1. **Input Node Configuration**

    ![Message Input Node Configuration](./condition/message-input-node.png)

2. **Condition Check Configuration**

    ```
    Condition Content: $input.message
    Condition 1: Starts With "/help"  // Help command
    Condition 2: Starts With "/search"  // Search command
    Condition 3: Contains "Order Number:"  // Order inquiry
    Condition 4: Contains empty string  // Regular message
    ```

    ![Condition Check Configuration](./condition/message-condition-node.png)

3. **Processing Node Configuration (for each branch)**

    For example, you can:

    - Help Command Branch: Return a list of help information.
    - Search Command Branch: Execute a search operation.
    - Order Inquiry Branch: Query the order status.
    - Regular Message Branch: Return a default reply.

### 3. File Name Matching Example

This example showcases how to use string-related condition checks, applicable for file processing, automated workflows, and similar scenarios:

```
Node Name: File Classification
Condition Content: $input.filename
Condition 1: Ends With ".jpg" or Ends With ".png" (Image Files)
Condition 2: Ends With ".pdf" (PDF Document)
Condition 3: Ends With ".zip" (Compressed Archive)
Condition 4: Other cases (Unknown Type)
```

![File Type Condition Check Configuration](./condition/file-condition-node.png)

## Advanced Usage

### 1. Nested Conditions

You can implement complex logic by connecting multiple condition nodes, which is useful in scenarios such as multi-level approvals or complex business rules:

```
[Age Check] --> [Identity Check] --> [Permission Check]
```

### 2. Combined Condition Example

Using multiple conditions in a single condition node is suitable for scenarios requiring several conditions to be met simultaneously:

```
Condition 1: Equal to "premium" and Contains "plus"  // Matches "premium plus"
Condition 2: Starts With "basic"  // Matches all entries beginning with "basic"
Condition 3: Other cases
```

## Best Practices

1. **Principles of Condition Design**

    - All comparisons are based on strings.
    - Conditions should be mutually exclusive.
    - Conditions should comprehensively cover all possible cases.
    - Special cases should be handled with priority.
    - Use comments to explain the business meaning behind each condition.
    - Keep the condition logic simple and maintainable.

2. **Variable Handling**

    - Ensure that the variable exists before performing condition checks.
    - Consider consistency in data types.
    - Be mindful of case sensitivity.

3. **Error Handling**

    - Add a default branch to handle exceptional cases.
    - Include logging in critical branches.
    - Consider the necessity of data validation.

## Common Issues

:::caution
Common mistakes:

1. **Order of Conditions**

    ```
    Condition Content: $input.message
    Condition 1: Contains "Hello"
    Condition 2: Equal to "Hello World"  // ❌ Incorrect: This condition will never match because conditions are evaluated sequentially.
    ```

    Correct approach:

    ```
    Condition Content: $input.message
    Condition 1: Equal to "Hello World"  // ✅ Correct: Check the more specific condition first.
    Condition 2: Contains "Hello"
    ```

2. **Omission of Cases**

    ```
    Condition 1: Equal to "Success"
    Condition 2: Equal to "Failure"
    // Other possible states are omitted.
    ```

    Correct approach: Add a default branch to handle all other cases.

3. **Case Sensitivity**

    ```
    Condition Content: $input.status
    Condition 1: Equal to "Success"  // Will not match "success"
    ```

    Solution: Ensure exact case matching when comparing strings.

4. **Performance Optimization**
    - Avoid overly nested conditions.
      :::

## Debugging Tips

**Using Output Nodes**

Add an output node on each branch to help debug the workflow:

```
[Condition Check] --> [Output Node (Prints the check result)]
```

By effectively utilizing condition nodes, you can construct flexible and powerful workflows. Remember to carefully plan your condition logic to ensure completeness and mutual exclusivity, thus guaranteeing the reliable execution of your workflow.
