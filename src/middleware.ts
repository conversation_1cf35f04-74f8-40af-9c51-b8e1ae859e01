import type { APIContext } from "astro";
import { sequence } from "astro:middleware";

async function rewriteSEO(context: APIContext, next: () => Promise<Response>) {
	const originalPath = context.url.pathname;

	// 排除_astro开头的资源路径
	if (originalPath.startsWith("/_astro") || originalPath.startsWith("/_image")) {
		return next();
	}

	// 检查路径是否包含下划线
	if (/_/.test(originalPath)) {
		// 将下划线替换为连字符
		const newPath = originalPath.replace(/_/g, "-");

		// 保留查询参数
		const newUrl = new URL(context.url);
		newUrl.pathname = newPath;

		// 使用301永久重定向（SEO友好）
		return Response.redirect(newUrl.toString(), 301);
	}

	// 如果没有下划线则继续正常流程
	return next();
}

export const onRequest = sequence(rewriteSEO);
