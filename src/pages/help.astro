---
import Layout from "../layouts/Layout.astro";
import QuestionMarkCircleIcon from "astro-heroicons/outline/QuestionMarkCircle.astro";
import BookOpenIcon from "astro-heroicons/outline/BookOpen.astro";
import CogIcon from "astro-heroicons/outline/Cog.astro";
import ChatBubbleLeftRightIcon from "astro-heroicons/outline/ChatBubbleLeftRight.astro";
import RocketLaunchIcon from "astro-heroicons/outline/RocketLaunch.astro";
import UserGroupIcon from "astro-heroicons/outline/UserGroup.astro";

const pageTitle = "帮助中心 - FlowAI";
const pageDescription =
	"FlowAI帮助中心，提供详细的使用指南、常见问题解答、功能介绍等，帮助您快速上手AI工作流构建和AI智能体开发。";
const keywords = "FlowAI帮助, 使用指南, 常见问题, AI工作流教程, AI智能体帮助, 技术支持";

const helpCategories = [
	{
		icon: RocketLaunchIcon,
		title: "快速开始",
		description: "新手入门指南，快速了解FlowAI基本功能",
		items: [
			{ title: "如何注册账户", link: "#register" },
			{ title: "创建第一个工作流", link: "#first-workflow" },
			{ title: "基本操作指南", link: "#basic-operations" },
			{ title: "界面功能介绍", link: "#interface-guide" },
		],
	},
	{
		icon: CogIcon,
		title: "工作流构建",
		description: "详细的工作流创建和管理指南",
		items: [
			{ title: "工作流节点介绍", link: "#workflow-nodes" },
			{ title: "节点连接与配置", link: "#node-configuration" },
			{ title: "变量与参数设置", link: "#variables-parameters" },
			{ title: "工作流调试技巧", link: "#debugging-tips" },
		],
	},
	{
		icon: ChatBubbleLeftRightIcon,
		title: "AI智能体",
		description: "AI Agent功能使用指南",
		items: [
			{ title: "AI Agent基础概念", link: "#agent-basics" },
			{ title: "工具调用配置", link: "#tool-configuration" },
			{ title: "MCP服务器设置", link: "#mcp-setup" },
			{ title: "智能体优化建议", link: "#agent-optimization" },
		],
	},
	{
		icon: BookOpenIcon,
		title: "高级功能",
		description: "进阶功能和最佳实践",
		items: [
			{ title: "API集成指南", link: "#api-integration" },
			{ title: "自定义节点开发", link: "#custom-nodes" },
			{ title: "批量处理技巧", link: "#batch-processing" },
			{ title: "性能优化建议", link: "#performance-optimization" },
		],
	},
];

const faqs = [
	{
		question: "如何开始使用FlowAI？",
		answer: '首先访问FlowAI官网注册账户，注册成功后会自动获得50积分。登录后点击"创建工作流"按钮，选择合适的模板或从空白开始，拖拽节点到画布上并连接它们，配置每个节点的参数，最后点击运行测试您的工作流。',
	},
	{
		question: "什么是AI Agent节点？",
		answer: "AI Agent是FlowAI的智能代理节点，具备自主决策能力，能够根据任务需求自动调用各种工具完成复杂任务。与普通LLM节点不同，AI Agent可以进行多步推理，自动选择和使用工具如网页抓取、计算器、HTTP请求等。",
	},
	{
		question: "如何配置MCP服务器？",
		answer: "在AI Agent节点设置中找到MCP服务器配置选项，输入MCP服务器地址和相关参数，选择连接类型（stream HTTP或SSE），保存配置后AI Agent就能自动使用该MCP服务器提供的功能。",
	},
	{
		question: "工作流运行失败怎么办？",
		answer: "首先检查节点配置是否正确，确认API密钥是否有效，查看错误日志获取具体错误信息。常见问题包括：API配额不足、网络连接问题、参数配置错误等。可以使用调试模式逐步检查每个节点的输出。",
	},
	{
		question: "如何优化工作流性能？",
		answer: "建议：1）合理设置并发数量；2）使用缓存减少重复计算；3）优化提示词减少token消耗；4）选择合适的AI模型；5）避免不必要的循环操作；6）定期清理无用的工作流和数据。",
	},
	{
		question: "积分如何计算和充值？",
		answer: "积分按照AI模型调用次数和复杂度计算，不同模型消耗不同。新用户注册赠送50积分，积分用完后可以使用自己的OpenAI API Key，或联系客服了解充值方案。",
	},
];
---

<Layout title={pageTitle} description={pageDescription} keywords={keywords}>
	<div class="max-w-6xl mx-auto px-4 py-12">
		<!-- 页面标题 -->
		<div class="text-center mb-12">
			<h1 class="text-4xl font-bold text-primary mb-4">帮助中心</h1>
			<p class="text-xl text-gray-600 max-w-3xl mx-auto">
				欢迎来到FlowAI帮助中心！这里有详细的使用指南、常见问题解答和最佳实践，帮助您快速掌握AI工作流构建和AI智能体开发。
			</p>
		</div>

		<!-- 搜索框 -->
		<div class="max-w-2xl mx-auto mb-12">
			<div class="relative">
				<input
					type="text"
					placeholder="搜索帮助内容..."
					class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
				/>
				<QuestionMarkCircleIcon
					class="absolute left-4 top-3.5 w-5 h-5 text-gray-400"
				/>
			</div>
		</div>

		<!-- 帮助分类 -->
		<div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
			{
				helpCategories.map((category) => (
					<div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow">
						<div class="flex items-center mb-4">
							<category.icon class="w-8 h-8 text-primary mr-3" />
							<h3 class="text-xl font-semibold">{category.title}</h3>
						</div>
						<p class="text-gray-600 mb-4">{category.description}</p>
						<ul class="space-y-2">
							{category.items.map((item) => (
								<li>
									<a
										href={item.link}
										class="text-primary hover:text-secondary transition-colors text-sm"
									>
										{item.title}
									</a>
								</li>
							))}
						</ul>
					</div>
				))
			}
		</div>

		<!-- 常见问题 -->
		<div class="mb-16">
			<h2 class="text-3xl font-bold text-center text-primary mb-8">常见问题</h2>
			<div class="max-w-4xl mx-auto space-y-4">
				{
					faqs.map((faq) => (
						<div class="bg-white rounded-lg shadow-sm border border-gray-200">
							<details class="group">
								<summary class="flex justify-between items-center font-medium cursor-pointer list-none p-6">
									<h3 class="text-lg">{faq.question}</h3>
									<span class="transition group-open:rotate-180">
										<svg
											fill="none"
											height="24"
											shape-rendering="geometricPrecision"
											stroke="currentColor"
											stroke-linecap="round"
											stroke-linejoin="round"
											stroke-width="1.5"
											viewBox="0 0 24 24"
											width="24"
										>
											<path d="M6 9l6 6 6-6" />
										</svg>
									</span>
								</summary>
								<div class="px-6 pb-6">
									<p class="text-gray-600">{faq.answer}</p>
								</div>
							</details>
						</div>
					))
				}
			</div>
		</div>

		<!-- 联系支持 -->
		<div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-8 text-center">
			<UserGroupIcon class="w-12 h-12 text-primary mx-auto mb-4" />
			<h3 class="text-2xl font-bold mb-4">还有其他问题？</h3>
			<p class="text-gray-600 mb-6">
				如果您在帮助中心没有找到答案，我们的支持团队随时为您提供帮助。
			</p>
			<div class="flex flex-col sm:flex-row gap-4 justify-center">
				<a href="/contact" class="btn btn-primary"> 联系我们 </a>
				<a href="/feedback" class="btn btn-outline btn-primary"> 提交反馈 </a>
			</div>
		</div>
	</div>
</Layout>

<style>
	details summary::-webkit-details-marker {
		display: none;
	}
</style>
