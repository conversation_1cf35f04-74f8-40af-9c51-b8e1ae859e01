---
import Layout from "../../layouts/Layout.astro";
import RocketLaunchIcon from "astro-heroicons/outline/RocketLaunch.astro";
import CommandLineIcon from "astro-heroicons/outline/CommandLine.astro";
import CursorArrowRaysIcon from "astro-heroicons/outline/CursorArrowRays.astro";
import ChatB<PERSON>bleLeftRightIcon from "astro-heroicons/outline/ChatBubbleLeftRight.astro";
import VariableIcon from "astro-heroicons/outline/Variable.astro";
import CreditCardIcon from "astro-heroicons/outline/CreditCard.astro";

const pageTitle = "FlowAI - AI Workflow Building and AI Agent Building Platform";
const pageDescription =
	"FlowAI is a powerful AI workflow building platform and AI agent building platform. Create automation workflows and intelligent agents without programming. Start for free, automate tasks, and enhance efficiency with powerful LLM support!";
const keywords =
	"AI workflows, AI agent building platform, agent development, automation tools, LLM, workflow automation, FlowAI, artificial intelligence workflows, AI Agent, intelligent agent, MCP server, tool calling, autonomous AI, multi-step reasoning, web scraping, HTTP requests, calculator tools, Model Context Protocol, AI agent platform, intelligent agent building";

const features = [
	{
		icon: CommandLineIcon,
		title: "No Programming Experience Required",
		desc: "Our platform is designed for users of all skill levels, allowing you to create and manage AI workflows without any programming knowledge. FlowAI makes it easy for everyone to get started and unleash their creativity on their automation journey.",
		img: "/feature1-en.png",
	},
	{
		icon: CursorArrowRaysIcon,
		title: "AI-Driven Automation",
		desc: "With FlowAI's AI-driven capabilities, you can automate repetitive tasks and significantly improve your work efficiency. Delegate tedious tasks to intelligent systems, giving you more time to focus on what truly matters and enhancing overall work quality.",
		img: "/feature2-en.png",
	},
	{
		icon: VariableIcon,
		title: "Multiple Workflow Nodes Available",
		desc: "FlowAI offers a variety of workflow nodes for you to choose from, allowing you to customize your processes based on different tasks and needs. Whether it's data processing, information transfer, or other tasks, our platform can meet your diverse requirements.",
		img: "/feature3-en.png",
	},
	{
		icon: ChatBubbleLeftRightIcon,
		title: "Supports Multiple Large Language Models",
		desc: "Our platform integrates various large language models (LLMs) to provide flexibility and powerful features for your workflows. Whether you need to process text, generate content, or perform complex analyses, FlowAI can offer you the best solutions.",
		img: "/feature4-en.png",
	},
	{
		icon: RocketLaunchIcon,
		title: "One-Click Run AI Workflows",
		desc: "No complex operations are needed; FlowAI generates an interactive UI for each workflow, allowing you to start and run your processes with just a click of a button, without the need to create a UI or write code. FlowAI automatically handles all the details for you, ensuring your processes are efficient and reliable.",
		img: "/feature5-en.png",
	},
];

const faqs = [
	{
		question: "What is FlowAI?",
		answer: "FlowAI is a powerful workflow building tool designed to help users leverage artificial intelligence (AI) technology to simplify and automate their workflows. Whether you are a beginner or an experienced user, FlowAI provides an intuitive interface and various features to help you easily create and manage workflows.",
	},
	{
		question: "Do I need programming experience to use FlowAI?",
		answer: "No! FlowAI is designed to be very user-friendly, suitable for users of all skill levels. You do not need any programming knowledge to create and manage workflows; you just need to follow simple steps to get started. Of course, if you have programming experience, it will be easier for you to get started and create more complex workflows.",
	},
	{
		question: "How does FlowAI help me improve work efficiency?",
		answer: "FlowAI harnesses the power of AI to automate repetitive tasks, allowing you to focus your time and energy on more important matters. By automating processes, you can reduce the time spent on manual operations, thereby improving overall work efficiency.",
	},
	{
		question: "What types of workflow nodes can I use?",
		answer: "FlowAI offers a variety of workflow nodes that you can choose from and customize based on different tasks and needs. These nodes can help you build complex workflows to meet your specific requirements.",
	},
	{
		question: "Which large language models does FlowAI support?",
		answer: "FlowAI integrates various large language models (LLMs) such as GPT-4o, Claude 3.5, and DeepSeek, providing users with flexibility and powerful features. You can choose the appropriate model based on your needs to achieve the best workflow results.",
	},
	{
		question: "How do I get started with FlowAI?",
		answer: "To get started with FlowAI, simply visit our official website, register an account, and then log in to your dashboard. Next, you can follow the prompts to create your first workflow and experience the powerful features of FlowAI.",
	},
	{
		question: "What is the pricing for FlowAI?",
		answer: "Currently, each user can use it for free. You will receive 50 points upon registration, and specific pricing information please see: https://flowai.cc/en/intro/price/",
	},
	{
		question: "Does FlowAI provide customer support?",
		answer: "Yes, FlowAI offers comprehensive customer support. If you encounter any issues while using the service, you can always contact our support team, and we will be happy to assist you.",
	},
	{
		question: "Can I create multiple workflows on FlowAI?",
		answer: "Absolutely! FlowAI allows users to create and manage multiple workflows, enabling you to create different workflows based on various projects and needs.",
	},
	{
		question: "What advantages does FlowAI have compared to other workflow tools?",
		answer: "There are many workflow tools available on the market, such as Dify and Doubao. Each tool has its own advantages, and the barrier to creating AI workflows is not high. FlowAI does not intend to compete with them; every user has their own usage habits, and the most popular tool may not necessarily be the most suitable. Users can try different tools to find the one that fits them best. FlowAI aims to provide a simple, easy-to-use, and efficient AI workflow tool that allows users to easily create and manage their workflows.",
	},
	{
		question: "What is FlowAI's AI Agent feature?",
		answer: "FlowAI's AI Agent is an intelligent agent node that possesses autonomous decision-making capabilities and can automatically call various tools to complete complex tasks. Unlike regular LLM nodes that only provide single AI inference results, AI Agent can perform multi-step reasoning and automatically select and use appropriate tools such as web scraping, calculators, HTTP requests, etc., to accomplish more complex automation workflows.",
	},
	{
		question: "What types of tool calls does AI Agent support?",
		answer: "FlowAI's AI Agent supports multiple types of tool calls, including but not limited to: web scraping tools (for obtaining web content), calculator tools (for mathematical calculations), HTTP request tools (for API calls), and more. AI Agent intelligently selects appropriate tools based on task requirements and automatically executes corresponding operations, greatly enhancing the intelligence level of workflows.",
	},
	{
		question: "What is an MCP server? How does FlowAI support MCP?",
		answer: "MCP (Model Context Protocol) is a model context protocol that allows AI models to communicate with external services in a standardized way. FlowAI's AI Agent node now fully supports MCP server configuration, including stream HTTP and SSE (Server-Sent Events) types. By configuring MCP servers, AI Agent can automatically use various functions and services provided by MCP, greatly expanding the capability boundaries of AI Agent.",
	},
	{
		question: "How do I configure and use MCP servers in FlowAI?",
		answer: "Configuring MCP servers in FlowAI is very simple: 1) Find the MCP server configuration option in the AI Agent node settings; 2) Enter the MCP server address and related parameters; 3) Select the connection type (stream HTTP or SSE); 4) After saving the configuration, AI Agent can automatically discover and use all tools and functions provided by that MCP server. This allows your workflows to integrate with more external services and capabilities.",
	},
	{
		question: "What's the difference between AI Agent and regular LLM nodes?",
		answer: "The main differences lie in intelligence level and functional scope: 1) Regular LLM nodes only perform single AI inference, directly outputting results after text input; 2) AI Agent has autonomous decision-making capabilities and can perform multi-step reasoning and planning; 3) AI Agent can automatically call tools such as web scraping, API requests, etc.; 4) AI Agent supports MCP server integration and can use more external services; 5) AI Agent is more suitable for handling complex scenarios that require multi-step operations.",
	},
	{
		question: "Does using AI Agent features require additional payment?",
		answer: "AI Agent functionality is included as one of the core features of the FlowAI platform in our standard service. You can use free credits or your own API Key to run AI Agent workflows. Since AI Agent may require multiple AI calls and tool usage, it might consume more credits compared to regular LLM nodes, but this entirely depends on the complexity of the task and the required operation steps.",
	},
];

const ldJson = {
	"@context": "https://schema.org",
	"@type": "FAQPage",
	mainEntity: faqs.map((faq) => ({
		"@type": "Question",
		name: faq.question,
		acceptedAnswer: {
			"@type": "Answer",
			text: faq.answer,
		},
	})),
	hasPart: {
		"@type": "ItemList",
		itemListElement: features.map((feature, index) => ({
			"@type": "ListItem",
			position: index + 1,
			item: {
				"@type": "HowToTip",
				name: feature.title,
				text: feature.desc,
				image: feature.img,
			},
		})),
	},
};

const userLang = Astro.request.headers.get("accept-language") || "";
const isChinese = userLang.startsWith("zh");
---

<Layout
	title={pageTitle}
	description={pageDescription}
	keywords={keywords}
	jsonLd={ldJson}
	lang="en"
>
	{
		isChinese && (
			<div class="bg-yellow-300 text-black p-4 text-center">
				<strong>提示：</strong>您可以调整到{" "}
				<a href="/" class="text-blue-600 underline" title="中文版">
					中文版
				</a>
				。
			</div>
		)
	}
	<!-- <div
		class="bg-gradient-to-r from-blue-50 to-indigo-50 py-2 px-4 text-center text-sm border-b border-blue-100 shadow-sm mb-2"
	>
		<span class="inline-flex items-center">
			<span class="relative flex h-2 w-2 mr-2">
				<span
					class="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary opacity-75"
				></span>
				<span class="relative inline-flex rounded-full h-2 w-2 bg-primary"></span>
			</span>
			<span>FlowAI now fully supports <b>GPT 4.1</b> model</span>
		</span>
	</div> -->
	<div class="mt-40"></div>

	<div class="text-center ml-6 mr-6">
		<h1 class="text-6xl font-bold max-sm:text-4xl relative">
			<span
				class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
			>
				<span class="sr-only">FlowAI - </span>AI Workflows
			</span>
			Made Simple
		</h1>
		<h2 class="text-3xl mt-5 max-sm:text-xl">
			Build AI Workflows and AI Agents with <span class="font-bold">FlowAI</span>
		</h2>

		<p class="mt-6 max-w-2xl mx-auto text-lg text-gray-600">
			Create powerful automation workflows and intelligent agents without code. Boost
			efficiency and unleash creativity with our professional <span
				class="text-primary font-medium">AI workflow</span
			> and <span class="text-primary font-medium">AI agent building</span> platform.
		</p>

		<a href="/dashboard" title="Start Your Journey">
			<button class="btn btn-lg btn-primary mt-8 px-10">
				<RocketLaunchIcon class="w-5 h-5 mr-2" /> Start For Free
			</button>
		</a>
		<div class="flex items-center justify-center mt-2 text-sm italic">
			<CreditCardIcon class="w-4 h-4 mr-2" />
			No credit card required - <span class="font-bold text-primary">50 credits</span> upon
			registration
		</div>

		<p class="text-md mt-10">
			Over <span class="font-bold">10,000+</span>
			<span class="text-primary">AI workflows</span> have been created
		</p>
	</div>

	<div
		class="relative mix-blend-darken mt-20 mx-auto self-center bg-gradient-to-r from-indigo-400 to-cyan-400 max-w-[1300px] w-[90%] h-[900px] max-lg:h-[650px] max-md:w-[95%] max-md:h-[500px] max-sm:h-[400px] rounded-2xl flex justify-center box-border overflow-hidden"
	>
		<video
			src="/index-en.mp4"
			class="w-full h-full object-cover"
			autoplay
			loop
			muted
			playsinline
			poster="/index-en-poster.png"
		>
		</video>
	</div>

	<div class="mt-20 text-center self-center">
		<h2 class="text-3xl font-bold text-primary">Easily and Quickly Create AI Workflows</h2>
		<p class="text-xl mt-2 max-w-3xl mx-auto">
			Use our intuitive and powerful tools to simplify your <span
				class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
				>AI workflow</span
			> creation process, making automation simple and efficient
		</p>
		<div
			class="flex flex-nowrap self-center max-w-[1500px] justify-start mt-10 w-screen overflow-x-scroll p-10 m-auto"
		>
			{
				features.map((feature) => (
					<div class="flex-none w-[500px] m-4 pt-6 bg-white rounded-xl overflow-hidden shadow-md text-left box-border">
						<div class="flex items-center mb-4 px-6">
							<feature.icon class="w-8 h-8 mr-2" />
							<h3 class="text-xl font-semibold">{feature.title}</h3>
						</div>
						<p class="mt-2 px-6">{feature.desc}</p>
						<img
							alt={feature.title}
							src={feature.img}
							class="w-full h-[300px] mt-5 object-cover"
						/>
					</div>
				))
			}
		</div>
	</div>

	<div class="mt-24 max-w-6xl mx-auto px-6">
		<div class="text-center mb-12">
			<h2 class="text-3xl font-bold text-primary">
				Why Choose FlowAI's <span class="font-normal">AI Workflow</span> and <span
					class="font-normal">AI Agent Building</span
				> Platform
			</h2>
			<p class="text-xl mt-3 max-w-3xl mx-auto">
				Our platform provides the best solution for creating professional <span
					class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
					>AI workflows</span
				> and <span
					class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
					>AI agents</span
				> for all types of users
			</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
			<div
				class="bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-xl shadow-sm border border-blue-100"
			>
				<h3 class="text-xl font-bold mb-4 text-primary">
					Enterprise-Grade <span class="font-normal">AI Workflow</span> and <span
						class="font-normal">AI Agent</span
					> Solutions
				</h3>
				<p class="text-gray-700">
					FlowAI provides powerful <span
						class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
						>AI workflow</span
					> and <span
						class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
						>AI agent</span
					> tools for businesses to help automate repetitive tasks, build intelligent
					agent systems, and improve team efficiency. Our platform supports multiple large
					language models and can be customized to meet your business needs.
				</p>
			</div>
			<div
				class="bg-gradient-to-br from-purple-50 to-pink-50 p-8 rounded-xl shadow-sm border border-purple-100"
			>
				<h3 class="text-xl font-bold mb-4 text-primary">
					<span class="font-normal">AI Workflow</span> and <span class="font-normal"
						>AI Agent</span
					> Assistant for Individual Creators
				</h3>
				<p class="text-gray-700">
					For individual creators, FlowAI's <span
						class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
						>AI workflow</span
					> and <span
						class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
						>AI agent building</span
					> platform provides easy-to-use tools to help you automate content creation,
					data processing, and other tasks, build personal AI assistants, allowing you
					to focus on creativity rather than repetitive work.
				</p>
			</div>
		</div>
	</div>

	<div class="mt-24 text-left self-center w-full max-w-4xl mx-auto px-4">
		<h2 class="text-3xl font-bold text-primary text-center">Frequently Asked Questions</h2>
		<p class="text-center text-lg mt-3 mb-10">
			Learn more about FlowAI's <span
				class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
				>AI workflow</span
			> platform
		</p>
		<div class="mt-10">
			{
				faqs.map((faq) => (
					<div class="mb-6">
						<details class="group w-full">
							<summary class="flex justify-between items-center font-medium cursor-pointer list-none w-full">
								<h3 class="text-lg text-left flex-grow">{faq.question}</h3>
								<span class="transition group-open:rotate-180 flex-shrink-0 ml-2">
									<svg
										fill="none"
										height="24"
										shape-rendering="geometricPrecision"
										stroke="currentColor"
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="1.5"
										viewBox="0 0 24 24"
										width="24"
									>
										<path d="M6 9l6 6 6-6" />
									</svg>
								</span>
							</summary>
							<p class="text-neutral-600 mt-3 group-open:animate-fadeIn">
								{faq.answer}
							</p>
						</details>
					</div>
				))
			}
		</div>
	</div>

	<div class="mt-24 relative overflow-hidden">
		<!-- 背景装饰 -->
		<div class="absolute inset-0 bg-gradient-to-br from-blue-50 via-cyan-50 to-blue-100">
		</div>
		<div class="absolute inset-0 opacity-30">
			<div
				class="absolute top-0 left-0 w-96 h-96 rounded-full transform -translate-x-1/2 -translate-y-1/2 radial-gradient-blue"
			>
			</div>
			<div
				class="absolute bottom-0 right-0 w-96 h-96 rounded-full transform translate-x-1/2 translate-y-1/2 radial-gradient-cyan"
			>
			</div>
		</div>

		<div class="relative py-20 px-6">
			<div class="max-w-4xl mx-auto text-center">
				<!-- 主标题 -->
				<div class="mb-8">
					<h2 class="text-4xl md:text-5xl font-bold text-primary mb-4 leading-tight">
						Start Building
						<span
							class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
						>
							AI Workflows
						</span>
						with FlowAI
					</h2>
					<div
						class="w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto rounded-full"
					>
					</div>
				</div>

				<!-- 描述文字 -->
				<p class="text-xl mb-10 max-w-3xl mx-auto text-gray-700 leading-relaxed">
					FlowAI makes <span class="font-semibold text-primary">AI workflow</span> building
					simple and efficient. Whether you're a business user or an individual creator,
					you can easily create professional
					<span class="font-semibold text-primary">AI workflows</span> through our platform.
					Sign up now to experience FlowAI's powerful features!
				</p>

				<!-- Logo和统计信息 -->
				<div class="mb-10">
					<img
						src="/main-logo-no-bg.png"
						class="block w-32 mx-auto mb-4"
						alt="FlowAI"
					/>
					<div class="flex flex-wrap justify-center gap-8 text-sm text-gray-600">
						<div class="flex items-center">
							<div class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse">
							</div>
							<span>10,000+ Workflows Created</span>
						</div>
						<div class="flex items-center">
							<div
								class="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse delay-500"
							>
							</div>
							<span>Multiple AI Models</span>
						</div>
						<div class="flex items-center">
							<div
								class="w-2 h-2 bg-purple-500 rounded-full mr-2 animate-pulse delay-1000"
							>
							</div>
							<span>Free Credits on Signup</span>
						</div>
					</div>
				</div>

				<!-- CTA按钮组 -->
				<div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
					<a href="/dashboard" title="Start Now!" class="group">
						<button
							class="btn btn-primary btn-lg px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
						>
							<RocketLaunchIcon
								class="w-6 h-6 mr-2 group-hover:animate-bounce"
							/>
							Start Building AI Workflows Now
						</button>
					</a>
					<a href="/en/intro/overview/" title="Learn More" class="group">
						<button
							class="btn btn-outline btn-primary btn-lg px-8 py-4 text-lg font-semibold hover:bg-primary hover:text-white transition-all duration-300"
						>
							<svg
								class="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
								></path>
							</svg>
							Learn More
						</button>
					</a>
				</div>

				<!-- 信任指标 -->
				<div class="mt-12 pt-8 border-t border-gray-200">
					<p class="text-sm text-gray-500 mb-4">Trusted by users worldwide</p>
					<div class="flex justify-center items-center space-x-8 opacity-60">
						<div class="text-xs text-gray-400">🚀 Fast Deployment</div>
						<div class="text-xs text-gray-400">🔒 Secure & Reliable</div>
						<div class="text-xs text-gray-400">⚡ Efficient Automation</div>
						<div class="text-xs text-gray-400">🎯 Precise Intelligence</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</Layout>

<style>
	details summary::-webkit-details-marker {
		display: none;
	}

	/* 自定义径向渐变背景 */
	.radial-gradient-blue {
		background: radial-gradient(
			circle,
			rgba(59, 130, 246, 0.15) 0%,
			rgba(59, 130, 246, 0.05) 50%,
			transparent 100%
		);
	}

	.radial-gradient-cyan {
		background: radial-gradient(
			circle,
			rgba(34, 211, 238, 0.15) 0%,
			rgba(34, 211, 238, 0.05) 50%,
			transparent 100%
		);
	}

	/* 增强动画效果 */
	@keyframes float {
		0%,
		100% {
			transform: translateY(0px);
		}
		50% {
			transform: translateY(-10px);
		}
	}

	.animate-float {
		animation: float 6s ease-in-out infinite;
	}

	/* 自定义脉冲动画 */
	@keyframes custom-pulse {
		0%,
		100% {
			opacity: 1;
			transform: scale(1);
		}
		50% {
			opacity: 0.5;
			transform: scale(1.1);
		}
	}

	.animate-custom-pulse {
		animation: custom-pulse 3s ease-in-out infinite;
	}
</style>
