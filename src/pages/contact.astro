---
import Layout from "../layouts/Layout.astro";
import EnvelopeIcon from "astro-heroicons/outline/Envelope.astro";

import MapPinIcon from "astro-heroicons/outline/MapPin.astro";
import ClockIcon from "astro-heroicons/outline/Clock.astro";
import ChatBubbleLeftRightIcon from "astro-heroicons/outline/ChatBubbleLeftRight.astro";
import UserIcon from "astro-heroicons/outline/User.astro";
import PaperAirplaneIcon from "astro-heroicons/outline/PaperAirplane.astro";

const pageTitle = "联系我们 - FlowAI";
const pageDescription =
	"联系FlowAI团队，获取技术支持、商务合作或其他咨询。我们提供多种联系方式，随时为您提供专业服务。";
const keywords = "FlowAI联系方式, 技术支持, 商务合作, 客服咨询, 联系我们";

const contactMethods = [
	{
		icon: EnvelopeIcon,
		title: "邮箱联系",
		content: "<EMAIL>",
		description: "发送邮件给我们，我们会在24小时内回复",
		action: "mailto:<EMAIL>",
	},
	{
		icon: ChatBubbleLeftRightIcon,
		title: "在线客服",
		content: "即时聊天支持",
		description: "工作时间内提供实时在线客服支持",
		action: "#",
	},
	{
		icon: ClockIcon,
		title: "服务时间",
		content: "周一至周五 9:00-18:00",
		description: "北京时间，节假日可能延迟回复",
		action: null,
	},
	{
		icon: MapPinIcon,
		title: "公司地址",
		content: "中国香港",
		description: "我们是一家专注于AI技术的创新公司",
		action: null,
	},
];

const contactReasons = [
	"技术支持",
	"商务合作",
	"产品咨询",
	"Bug反馈",
	"功能建议",
	"账户问题",
	"其他",
];
---

<Layout title={pageTitle} description={pageDescription} keywords={keywords}>
	<!-- Hero Section -->
	<div
		class="hero min-h-[50vh] bg-gradient-to-br from-primary/10 via-secondary/5 to-accent/10"
	>
		<div class="hero-content text-center">
			<div class="max-w-4xl">
				<div class="flex justify-center mb-6">
					<div class="p-4 bg-primary/10 rounded-full">
						<ChatBubbleLeftRightIcon class="w-16 h-16 text-primary" />
					</div>
				</div>
				<h1 class="text-5xl font-bold mb-6">
					<span
						class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent"
					>
						联系我们
					</span>
				</h1>
				<p class="text-xl text-base-content/70 mb-8 max-w-2xl mx-auto leading-relaxed">
					我们很乐意听到您的声音！无论您有技术问题、商务咨询还是产品建议，我们的团队都会及时为您提供专业的帮助和支持。
				</p>
			</div>
		</div>
	</div>

	<div class="max-w-7xl mx-auto px-4 py-16">
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
			<!-- 联系方式 -->
			<div>
				<h2 class="text-3xl font-bold mb-8">
					<span
						class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent"
					>
						联系方式
					</span>
				</h2>
				<div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
					{
						contactMethods.map((method) => (
							<div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300">
								<div class="card-body">
									<div class="flex justify-center mb-4">
										<div class="p-3 bg-primary/10 rounded-full">
											<method.icon class="w-8 h-8 text-primary" />
										</div>
									</div>
									<h3 class="card-title justify-center text-lg mb-2">
										{method.title}
									</h3>
									{method.action ? (
										<a
											href={method.action}
											class="text-primary hover:text-secondary transition-colors font-medium text-center"
										>
											{method.content}
										</a>
									) : (
										<p class="font-medium text-center">{method.content}</p>
									)}
									<p class="text-base-content/70 text-sm text-center mt-2">
										{method.description}
									</p>
								</div>
							</div>
						))
					}
				</div>

				<!-- 快速链接 -->
				<div class="mt-12">
					<h3 class="text-2xl font-bold mb-6">
						<span
							class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent"
						>
							快速链接
						</span>
					</h3>
					<div class="grid grid-cols-2 gap-4">
						<a href="/help" class="btn btn-outline btn-primary gap-2">
							<ChatBubbleLeftRightIcon class="w-5 h-5" />
							帮助中心
						</a>
						<a href="/feedback" class="btn btn-outline btn-secondary gap-2">
							<PaperAirplaneIcon class="w-5 h-5" />
							意见反馈
						</a>
						<a href="/intro/overview/" class="btn btn-outline btn-accent gap-2">
							<UserIcon class="w-5 h-5" />
							使用文档
						</a>
						<a href="/intro/price/" class="btn btn-outline btn-info gap-2">
							<ClockIcon class="w-5 h-5" />
							定价方案
						</a>
					</div>
				</div>
			</div>

			<!-- 联系表单 -->
			<div>
				<h2 class="text-3xl font-bold mb-8">
					<span
						class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent"
					>
						发送消息
					</span>
				</h2>
				<div class="card bg-base-100 shadow-2xl">
					<div class="card-body">
						<form class="space-y-6">
							<div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
								<div class="form-control">
									<label class="label">
										<span class="label-text font-medium">姓名 *</span>
									</label>
									<input
										type="text"
										id="name"
										name="name"
										required
										class="input input-bordered input-primary"
										placeholder="请输入您的姓名"
									/>
								</div>
								<div class="form-control">
									<label class="label">
										<span class="label-text font-medium">邮箱 *</span>
									</label>
									<input
										type="email"
										id="email"
										name="email"
										required
										class="input input-bordered input-primary"
										placeholder="请输入您的邮箱"
									/>
								</div>
							</div>

							<div class="form-control">
								<label class="label">
									<span class="label-text font-medium">联系原因 *</span>
								</label>
								<select
									id="subject"
									name="subject"
									required
									class="select select-bordered select-primary"
								>
									<option value="">请选择联系原因</option>
									{
										contactReasons.map((reason) => (
											<option value={reason}>{reason}</option>
										))
									}
								</select>
							</div>

							<div class="form-control">
								<label class="label">
									<span class="label-text font-medium"
										>公司/组织（可选）</span
									>
								</label>
								<input
									type="text"
									id="company"
									name="company"
									class="input input-bordered"
									placeholder="请输入您的公司或组织名称"
								/>
							</div>

							<div class="form-control">
								<label class="label">
									<span class="label-text font-medium">详细描述 *</span>
								</label>
								<textarea
									id="message"
									name="message"
									rows="5"
									required
									class="textarea textarea-bordered textarea-primary"
									placeholder="请详细描述您的问题或需求..."></textarea>
							</div>

							<div class="form-control">
								<label class="label cursor-pointer justify-start gap-3">
									<input
										type="checkbox"
										id="privacy"
										name="privacy"
										required
										class="checkbox checkbox-primary"
									/>
									<span class="label-text">
										我已阅读并同意
										<a href="/privacy-policy" class="link link-primary"
											>隐私政策</a
										>
									</span>
								</label>
							</div>

							<button type="submit" class="btn btn-primary btn-lg w-full gap-2">
								<PaperAirplaneIcon class="w-5 h-5" />
								发送消息
							</button>
						</form>
					</div>
				</div>
			</div>
		</div>

		<!-- 常见问题提示 -->
		<div class="hero bg-gradient-to-br from-info/10 to-primary/10 rounded-3xl mt-16">
			<div class="hero-content text-center py-12">
				<div class="max-w-md">
					<h3 class="text-3xl font-bold mb-4">在联系我们之前</h3>
					<p class="text-base-content/70 mb-8 leading-relaxed">
						您可能会在我们的帮助中心找到问题的答案，这样可以更快地解决您的问题。
					</p>
					<a href="/help" class="btn btn-primary btn-lg gap-2">
						<ChatBubbleLeftRightIcon class="w-5 h-5" />
						查看帮助中心
					</a>
				</div>
			</div>
		</div>
	</div>
</Layout>

<script>
	// 表单提交处理
	document.querySelector("form")?.addEventListener("submit", function (e) {
		e.preventDefault();

		// 获取表单数据
		const formData = new FormData(this as HTMLFormElement);

		// 这里可以添加实际的表单提交逻辑
		// 比如发送到后端API或第三方服务
		console.log("Form data:", Object.fromEntries(formData));

		alert("感谢您的消息！我们会尽快回复您。");
		this.reset();
	});
</script>
