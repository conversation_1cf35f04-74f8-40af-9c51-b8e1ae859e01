---
import Layout from "../layouts/Layout.astro";
import EnvelopeIcon from "astro-heroicons/outline/Envelope.astro";
import PhoneIcon from "astro-heroicons/outline/Phone.astro";
import MapPinIcon from "astro-heroicons/outline/MapPin.astro";
import ClockIcon from "astro-heroicons/outline/Clock.astro";
import ChatBubbleLeftRightIcon from "astro-heroicons/outline/ChatBubbleLeftRight.astro";
import UserIcon from "astro-heroicons/outline/User.astro";
import PaperAirplaneIcon from "astro-heroicons/outline/PaperAirplane.astro";

const pageTitle = "联系我们 - FlowAI";
const pageDescription = "联系FlowAI团队，获取技术支持、商务合作或其他咨询。我们提供多种联系方式，随时为您提供专业服务。";
const keywords = "FlowAI联系方式, 技术支持, 商务合作, 客服咨询, 联系我们";

const contactMethods = [
	{
		icon: EnvelopeIcon,
		title: "邮箱联系",
		content: "<EMAIL>",
		description: "发送邮件给我们，我们会在24小时内回复",
		action: "mailto:<EMAIL>"
	},
	{
		icon: ChatBubbleLeftRightIcon,
		title: "在线客服",
		content: "即时聊天支持",
		description: "工作时间内提供实时在线客服支持",
		action: "#"
	},
	{
		icon: ClockIcon,
		title: "服务时间",
		content: "周一至周五 9:00-18:00",
		description: "北京时间，节假日可能延迟回复",
		action: null
	},
	{
		icon: MapPinIcon,
		title: "公司地址",
		content: "中国香港",
		description: "我们是一家专注于AI技术的创新公司",
		action: null
	}
];

const contactReasons = [
	"技术支持",
	"商务合作",
	"产品咨询",
	"Bug反馈",
	"功能建议",
	"账户问题",
	"其他"
];
---

<Layout title={pageTitle} description={pageDescription} keywords={keywords}>
	<div class="max-w-6xl mx-auto px-4 py-12">
		<!-- 页面标题 -->
		<div class="text-center mb-12">
			<h1 class="text-4xl font-bold text-primary mb-4">联系我们</h1>
			<p class="text-xl text-gray-600 max-w-3xl mx-auto">
				我们很乐意听到您的声音！无论您有技术问题、商务咨询还是产品建议，我们的团队都会及时为您提供专业的帮助和支持。
			</p>
		</div>

		<div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
			<!-- 联系方式 -->
			<div>
				<h2 class="text-2xl font-bold mb-6">联系方式</h2>
				<div class="space-y-6">
					{contactMethods.map((method) => (
						<div class="flex items-start space-x-4 p-4 bg-white rounded-lg shadow-sm border border-gray-200">
							<div class="flex-shrink-0">
								<method.icon class="w-6 h-6 text-primary" />
							</div>
							<div class="flex-grow">
								<h3 class="font-semibold text-lg mb-1">{method.title}</h3>
								{method.action ? (
									<a 
										href={method.action} 
										class="text-primary hover:text-secondary transition-colors font-medium"
									>
										{method.content}
									</a>
								) : (
									<p class="text-gray-800 font-medium">{method.content}</p>
								)}
								<p class="text-gray-600 text-sm mt-1">{method.description}</p>
							</div>
						</div>
					))}
				</div>

				<!-- 快速链接 -->
				<div class="mt-8">
					<h3 class="text-xl font-semibold mb-4">快速链接</h3>
					<div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
						<a href="/help" class="flex items-center p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
							<ChatBubbleLeftRightIcon class="w-5 h-5 text-primary mr-3" />
							<span>帮助中心</span>
						</a>
						<a href="/feedback" class="flex items-center p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
							<PaperAirplaneIcon class="w-5 h-5 text-green-600 mr-3" />
							<span>意见反馈</span>
						</a>
						<a href="/intro/overview/" class="flex items-center p-3 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
							<UserIcon class="w-5 h-5 text-purple-600 mr-3" />
							<span>使用文档</span>
						</a>
						<a href="/intro/price/" class="flex items-center p-3 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors">
							<ClockIcon class="w-5 h-5 text-orange-600 mr-3" />
							<span>定价方案</span>
						</a>
					</div>
				</div>
			</div>

			<!-- 联系表单 -->
			<div>
				<h2 class="text-2xl font-bold mb-6">发送消息</h2>
				<form class="space-y-6 bg-white p-6 rounded-xl shadow-md border border-gray-200">
					<div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
						<div>
							<label for="name" class="block text-sm font-medium text-gray-700 mb-2">
								姓名 *
							</label>
							<input
								type="text"
								id="name"
								name="name"
								required
								class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
								placeholder="请输入您的姓名"
							/>
						</div>
						<div>
							<label for="email" class="block text-sm font-medium text-gray-700 mb-2">
								邮箱 *
							</label>
							<input
								type="email"
								id="email"
								name="email"
								required
								class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
								placeholder="请输入您的邮箱"
							/>
						</div>
					</div>

					<div>
						<label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
							联系原因 *
						</label>
						<select
							id="subject"
							name="subject"
							required
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
						>
							<option value="">请选择联系原因</option>
							{contactReasons.map((reason) => (
								<option value={reason}>{reason}</option>
							))}
						</select>
					</div>

					<div>
						<label for="company" class="block text-sm font-medium text-gray-700 mb-2">
							公司/组织（可选）
						</label>
						<input
							type="text"
							id="company"
							name="company"
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
							placeholder="请输入您的公司或组织名称"
						/>
					</div>

					<div>
						<label for="message" class="block text-sm font-medium text-gray-700 mb-2">
							详细描述 *
						</label>
						<textarea
							id="message"
							name="message"
							rows="5"
							required
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
							placeholder="请详细描述您的问题或需求..."
						></textarea>
					</div>

					<div class="flex items-center">
						<input
							type="checkbox"
							id="privacy"
							name="privacy"
							required
							class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
						/>
						<label for="privacy" class="ml-2 block text-sm text-gray-700">
							我已阅读并同意 <a href="/privacy-policy" class="text-primary hover:text-secondary">隐私政策</a>
						</label>
					</div>

					<button
						type="submit"
						class="w-full btn btn-primary py-3 text-lg font-semibold"
					>
						<PaperAirplaneIcon class="w-5 h-5 mr-2" />
						发送消息
					</button>
				</form>
			</div>
		</div>

		<!-- 常见问题提示 -->
		<div class="mt-16 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-8 text-center">
			<h3 class="text-2xl font-bold mb-4">在联系我们之前</h3>
			<p class="text-gray-600 mb-6">
				您可能会在我们的帮助中心找到问题的答案，这样可以更快地解决您的问题。
			</p>
			<a href="/help" class="btn btn-outline btn-primary">
				查看帮助中心
			</a>
		</div>
	</div>
</Layout>

<script>
	// 表单提交处理
	document.querySelector('form')?.addEventListener('submit', function(e) {
		e.preventDefault();
		
		// 获取表单数据
		const formData = new FormData(this);
		const data = Object.fromEntries(formData);
		
		// 这里可以添加实际的表单提交逻辑
		// 比如发送到后端API或第三方服务
		
		alert('感谢您的消息！我们会尽快回复您。');
		this.reset();
	});
</script>
