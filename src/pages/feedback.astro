---
import Layout from "../layouts/Layout.astro";
import ChatBubbleLeftRightIcon from "astro-heroicons/outline/ChatBubbleLeftRight.astro";
import LightBulbIcon from "astro-heroicons/outline/LightBulb.astro";
import ExclamationTriangleIcon from "astro-heroicons/outline/ExclamationTriangle.astro";
import HeartIcon from "astro-heroicons/outline/Heart.astro";
import PaperAirplaneIcon from "astro-heroicons/outline/PaperAirplane.astro";
import StarIcon from "astro-heroicons/outline/Star.astro";
import UserIcon from "astro-heroicons/outline/User.astro";
import EnvelopeIcon from "astro-heroicons/outline/Envelope.astro";

const pageTitle = "意见反馈 - FlowAI";
const pageDescription =
	"向FlowAI团队提交您的宝贵意见和建议，帮助我们改进产品，为用户提供更好的AI工作流构建体验。";
const keywords = "FlowAI反馈, 产品建议, Bug报告, 功能请求, 用户体验, 产品改进";

const feedbackTypes = [
	{
		icon: LightBulbIcon,
		type: "feature",
		title: "功能建议",
		description: "建议新功能或改进现有功能",
		color: "blue",
	},
	{
		icon: ExclamationTriangleIcon,
		type: "bug",
		title: "Bug报告",
		description: "报告您遇到的问题或错误",
		color: "red",
	},
	{
		icon: HeartIcon,
		type: "praise",
		title: "表扬建议",
		description: "分享您的正面体验和建议",
		color: "green",
	},
	{
		icon: ChatBubbleLeftRightIcon,
		type: "general",
		title: "一般反馈",
		description: "其他意见、建议或想法",
		color: "purple",
	},
];

const priorityLevels = [
	{ value: "low", label: "低优先级", description: "一般性建议或改进" },
	{ value: "medium", label: "中优先级", description: "影响使用体验的问题" },
	{ value: "high", label: "高优先级", description: "严重影响功能使用" },
	{ value: "urgent", label: "紧急", description: "系统无法正常使用" },
];
---

<Layout title={pageTitle} description={pageDescription} keywords={keywords}>
	<div class="max-w-4xl mx-auto px-4 py-12">
		<!-- 页面标题 -->
		<div class="text-center mb-12">
			<h1 class="text-4xl font-bold text-primary mb-4">意见反馈</h1>
			<p class="text-xl text-gray-600 max-w-3xl mx-auto">
				您的反馈对我们非常重要！请告诉我们您的想法、建议或遇到的问题，帮助我们不断改进FlowAI，为您提供更好的AI工作流构建体验。
			</p>
		</div>

		<!-- 反馈类型选择 -->
		<div class="mb-8">
			<h2 class="text-2xl font-semibold mb-6 text-center">选择反馈类型</h2>
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				{
					feedbackTypes.map((type) => (
						<div
							class={`feedback-type-card cursor-pointer p-4 rounded-lg border-2 transition-all hover:shadow-md ${
								type.color === "blue"
									? "border-blue-200 hover:border-blue-400"
									: type.color === "red"
										? "border-red-200 hover:border-red-400"
										: type.color === "green"
											? "border-green-200 hover:border-green-400"
											: "border-purple-200 hover:border-purple-400"
							}`}
							data-type={type.type}
						>
							<div class="text-center">
								<type.icon
									class={`w-8 h-8 mx-auto mb-3 ${
										type.color === "blue"
											? "text-blue-500"
											: type.color === "red"
												? "text-red-500"
												: type.color === "green"
													? "text-green-500"
													: "text-purple-500"
									}`}
								/>
								<h3 class="font-semibold mb-2">{type.title}</h3>
								<p class="text-sm text-gray-600">{type.description}</p>
							</div>
						</div>
					))
				}
			</div>
		</div>

		<!-- 反馈表单 -->
		<div class="bg-white rounded-xl shadow-md border border-gray-200 p-8">
			<form id="feedback-form" class="space-y-6">
				<!-- 隐藏的反馈类型字段 -->
				<input type="hidden" id="feedback-type" name="feedback-type" value="" />

				<!-- 基本信息 -->
				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<div>
						<label for="name" class="block text-sm font-medium text-gray-700 mb-2">
							<UserIcon class="w-4 h-4 inline mr-1" />
							姓名（可选）
						</label>
						<input
							type="text"
							id="name"
							name="name"
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
							placeholder="请输入您的姓名"
						/>
					</div>
					<div>
						<label
							for="email"
							class="block text-sm font-medium text-gray-700 mb-2"
						>
							<EnvelopeIcon class="w-4 h-4 inline mr-1" />
							邮箱（可选）
						</label>
						<input
							type="email"
							id="email"
							name="email"
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
							placeholder="如需回复请提供邮箱"
						/>
					</div>
				</div>

				<!-- 反馈标题 -->
				<div>
					<label for="title" class="block text-sm font-medium text-gray-700 mb-2">
						反馈标题 *
					</label>
					<input
						type="text"
						id="title"
						name="title"
						required
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
						placeholder="请简要描述您的反馈内容"
					/>
				</div>

				<!-- 优先级 -->
				<div>
					<label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
						优先级
					</label>
					<select
						id="priority"
						name="priority"
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
					>
						{
							priorityLevels.map((level) => (
								<option value={level.value}>
									{level.label} - {level.description}
								</option>
							))
						}
					</select>
				</div>

				<!-- 详细描述 -->
				<div>
					<label
						for="description"
						class="block text-sm font-medium text-gray-700 mb-2"
					>
						详细描述 *
					</label>
					<textarea
						id="description"
						name="description"
						rows="6"
						required
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
						placeholder="请详细描述您的反馈内容，包括：
- 具体的问题或建议
- 期望的结果
- 重现步骤（如果是Bug）
- 其他相关信息"
					></textarea>
				</div>

				<!-- 浏览器和系统信息 -->
				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<div>
						<label
							for="browser"
							class="block text-sm font-medium text-gray-700 mb-2"
						>
							浏览器信息（可选）
						</label>
						<input
							type="text"
							id="browser"
							name="browser"
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
							placeholder="例如：Chrome 120.0"
						/>
					</div>
					<div>
						<label for="os" class="block text-sm font-medium text-gray-700 mb-2">
							操作系统（可选）
						</label>
						<input
							type="text"
							id="os"
							name="os"
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
							placeholder="例如：Windows 11, macOS 14"
						/>
					</div>
				</div>

				<!-- 满意度评分 -->
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-3">
						总体满意度
					</label>
					<div class="flex items-center space-x-2">
						{
							[1, 2, 3, 4, 5].map((rating) => (
								<button
									type="button"
									class="star-rating p-1 hover:scale-110 transition-transform"
									data-rating={rating}
								>
									<StarIcon class="w-8 h-8 text-gray-300 hover:text-yellow-400" />
								</button>
							))
						}
						<span class="ml-4 text-sm text-gray-600">点击星星进行评分</span>
					</div>
					<input type="hidden" id="rating" name="rating" value="0" />
				</div>

				<!-- 提交按钮 -->
				<div class="flex justify-center pt-4">
					<button
						type="submit"
						class="btn btn-primary btn-lg px-8 py-3 text-lg font-semibold"
					>
						<PaperAirplaneIcon class="w-5 h-5 mr-2" />
						提交反馈
					</button>
				</div>
			</form>
		</div>

		<!-- 感谢信息 -->
		<div
			class="mt-12 bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-8 text-center"
		>
			<HeartIcon class="w-12 h-12 text-red-500 mx-auto mb-4" />
			<h3 class="text-2xl font-bold mb-4">感谢您的反馈！</h3>
			<p class="text-gray-600 mb-6">
				每一条反馈都是我们前进的动力。我们会认真对待您的每一个建议，持续改进FlowAI，为您提供更好的服务。
			</p>
			<div class="flex flex-col sm:flex-row gap-4 justify-center">
				<a href="/help" class="btn btn-outline btn-primary"> 查看帮助中心 </a>
				<a href="/contact" class="btn btn-outline btn-primary"> 联系我们 </a>
			</div>
		</div>
	</div>
</Layout>

<script>
	// 反馈类型选择
	document.querySelectorAll(".feedback-type-card").forEach((card) => {
		card.addEventListener("click", function (this: HTMLElement) {
			// 移除其他卡片的选中状态
			document.querySelectorAll(".feedback-type-card").forEach((c) => {
				c.classList.remove("ring-2", "ring-primary", "bg-blue-50");
			});

			// 添加选中状态
			this.classList.add("ring-2", "ring-primary", "bg-blue-50");

			// 设置隐藏字段值
			const type = this.dataset.type;
			const feedbackTypeField = document.getElementById(
				"feedback-type"
			) as HTMLInputElement;
			if (feedbackTypeField && type) {
				feedbackTypeField.value = type;
			}
		});
	});

	// 星级评分
	document.querySelectorAll(".star-rating").forEach((star) => {
		star.addEventListener("click", function () {
			const rating = parseInt(this.dataset.rating);
			document.getElementById("rating").value = rating;

			// 更新星星显示
			document.querySelectorAll(".star-rating").forEach((s, index) => {
				const starIcon = s.querySelector("svg");
				if (index < rating) {
					starIcon.classList.remove("text-gray-300");
					starIcon.classList.add("text-yellow-400");
				} else {
					starIcon.classList.remove("text-yellow-400");
					starIcon.classList.add("text-gray-300");
				}
			});
		});
	});

	// 自动填充浏览器和系统信息
	document.addEventListener("DOMContentLoaded", function () {
		// 自动填充浏览器信息
		const browserField = document.getElementById("browser");
		if (browserField && navigator.userAgent) {
			const ua = navigator.userAgent;
			let browserInfo = "";

			if (ua.includes("Chrome")) {
				const match = ua.match(/Chrome\/([0-9.]+)/);
				browserInfo = match ? `Chrome ${match[1]}` : "Chrome";
			} else if (ua.includes("Firefox")) {
				const match = ua.match(/Firefox\/([0-9.]+)/);
				browserInfo = match ? `Firefox ${match[1]}` : "Firefox";
			} else if (ua.includes("Safari") && !ua.includes("Chrome")) {
				const match = ua.match(/Version\/([0-9.]+)/);
				browserInfo = match ? `Safari ${match[1]}` : "Safari";
			} else if (ua.includes("Edge")) {
				const match = ua.match(/Edge\/([0-9.]+)/);
				browserInfo = match ? `Edge ${match[1]}` : "Edge";
			}

			browserField.placeholder = browserInfo || browserField.placeholder;
		}

		// 自动填充操作系统信息
		const osField = document.getElementById("os");
		if (osField && navigator.platform) {
			let osInfo = "";
			const platform = navigator.platform;

			if (platform.includes("Win")) {
				osInfo = "Windows";
			} else if (platform.includes("Mac")) {
				osInfo = "macOS";
			} else if (platform.includes("Linux")) {
				osInfo = "Linux";
			} else if (platform.includes("iPhone") || platform.includes("iPad")) {
				osInfo = "iOS";
			} else if (platform.includes("Android")) {
				osInfo = "Android";
			}

			osField.placeholder = osInfo || osField.placeholder;
		}
	});

	// 表单提交处理
	document.getElementById("feedback-form").addEventListener("submit", function (e) {
		e.preventDefault();

		// 获取表单数据
		const formData = new FormData(this);
		const data = Object.fromEntries(formData);

		// 验证必填字段
		if (!data.title || !data.description) {
			alert("请填写反馈标题和详细描述");
			return;
		}

		// 这里可以添加实际的表单提交逻辑
		// 比如发送到后端API或第三方服务

		alert("感谢您的反馈！我们已收到您的意见，会认真考虑并持续改进。");
		this.reset();

		// 重置星级评分
		document.querySelectorAll(".star-rating svg").forEach((star) => {
			star.classList.remove("text-yellow-400");
			star.classList.add("text-gray-300");
		});

		// 重置反馈类型选择
		document.querySelectorAll(".feedback-type-card").forEach((card) => {
			card.classList.remove("ring-2", "ring-primary", "bg-blue-50");
		});
	});
</script>

<style>
	.star-rating:hover svg {
		transform: scale(1.1);
	}
</style>
