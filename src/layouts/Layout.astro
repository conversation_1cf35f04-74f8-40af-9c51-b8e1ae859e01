---
import BaseLayout from "./base.astro";
import Header from "../components/header.astro";
import Footer from "../components/footer.astro";
import EnHeader from "../components/en/header.astro";
import EnFooter from "../components/en/footer.astro";
interface Props {
	title: string;
	description?: string;
	keywords?: string;
	jsonLd?: object;
	lang?: string;
}

const { title, description, keywords, jsonLd, lang } = Astro.props;
---

<BaseLayout title={title} description={description} keywords={keywords} jsonLd={jsonLd}>
	<div class="mb-20"></div>
	{lang === "en" ? <EnHeader slot="header" /> : <Header slot="header" />}

	<slot />

	{lang === "en" ? <EnFooter slot="footer" /> : <Footer slot="footer" />}
</BaseLayout>
