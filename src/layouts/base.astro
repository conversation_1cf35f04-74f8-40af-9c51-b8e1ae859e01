---
const { title, description, keywords, jsonLd } = Astro.props;
const availableLanguages = ["zh", "en"];
let currentLanguage = Astro.url.pathname.split("/")[1];
if (!availableLanguages.includes(currentLanguage)) {
	currentLanguage = "zh";
}

// 获取当前页面的完整URL
const canonicalURL = new URL(Astro.url.pathname, Astro.site);
const langURL = availableLanguages.map((lang) => {
	let pathname = Astro.url.pathname.replace(`/${currentLanguage}`, "");
	let site = Astro.site?.origin;
	if (lang === "zh") {
		return {
			lang,
			url: `${site}${pathname}`,
		};
	}
	return {
		lang,
		url: `${site}/${lang}${pathname}`,
	};
});
const ldJsonString = JSON.stringify(jsonLd) || "";
---

<!doctype html>
<html lang={currentLanguage} class="bg-base-100">
	<head>
		<meta charset="UTF-8" />
		<title>{title}</title>
		<meta
			name="viewport"
			content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
		/>
		<meta
			name="description"
			content={description ||
				"FlowAI 是一个 AI 工作流平台，可以帮助你自动化你的工作流程。"}
		/>
		<meta
			name="keywords"
			content={keywords ||
				"AI 工作流, 自动化工具, 无代码, 人工智能, 自动化, 工作流, 构建, 工具, 编程, 人工智能, 自动化, 工作流, 构建, 工具, 编程"}
		/>
		<meta name="google-adsense-account" content="ca-pub-****************" />
		<link rel="canonical" href={canonicalURL} />
		<link rel="icon" href="/favicon.ico" />
		<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
		<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
		<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
		<link rel="sitemap" href="/sitemap-index.xml" />
		<script type="application/ld+json" set:html={ldJsonString} />
		{
			langURL.map((lang) => {
				return <link rel="alternate" hreflang={lang.lang} href={lang.url} />;
			})
		}

		<script
			is:inline
			type="text/partytown"
			async
			src="https://www.googletagmanager.com/gtag/js?id=G-ZHKDPMY03J"></script>
		<script type="text/partytown">
			window.dataLayer = window.dataLayer || [];
			function gtag() {
				dataLayer.push(arguments);
			}
			gtag("js", new Date());
			gtag("config", "G-ZHKDPMY03J");
		</script>
		<script type="text/javascript" is:inline>
			(function (c, l, a, r, i, t, y) {
				c[a] =
					c[a] ||
					function () {
						(c[a].q = c[a].q || []).push(arguments);
					};
				t = l.createElement(r);
				t.async = 1;
				t.src = "https://www.clarity.ms/tag/" + i;
				y = l.getElementsByTagName(r)[0];
				y.parentNode.insertBefore(t, y);
			})(window, document, "clarity", "script", "plt8z3yibx");
		</script>
		<script is:inline defer>
			var _hmt = _hmt || [];
			(function () {
				var hm = document.createElement("script");
				hm.src = "https://hm.baidu.com/hm.js?976b5c60ea7e17a453980f639a6ac644";
				var s = document.getElementsByTagName("script")[0];
				s.parentNode.insertBefore(hm, s);
			})();
		</script>
	</head>
	<body class="antialiased">
		<div class="flex flex-col min-h-screen bg-base-100">
			<slot name="header" />
			<main class="flex-grow">
				<slot />
			</main>

			<footer class="text-center py-6 text-sm text-gray-500 bg-base-200">
				<slot name="footer" />
			</footer>

			<!-- 回到顶部按钮 -->
			<button
				id="back-to-top"
				class="fixed bottom-8 right-8 bg-primary text-white p-3 rounded-full shadow-lg hover:bg-primary/90 transition-all duration-300 transform hover:scale-110 opacity-0 invisible z-50"
				title="回到顶部"
				aria-label="回到顶部"
			>
				<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
				</svg>
			</button>
		</div>

		<script>
			// 回到顶部按钮功能
			const backToTopButton = document.getElementById("back-to-top");

			if (backToTopButton) {
				// 监听滚动事件
				window.addEventListener("scroll", () => {
					if (window.pageYOffset > 300) {
						backToTopButton.classList.remove("opacity-0", "invisible");
						backToTopButton.classList.add("opacity-100", "visible");
					} else {
						backToTopButton.classList.add("opacity-0", "invisible");
						backToTopButton.classList.remove("opacity-100", "visible");
					}
				});

				// 点击回到顶部
				backToTopButton.addEventListener("click", () => {
					window.scrollTo({
						top: 0,
						behavior: "smooth",
					});
				});
			}
		</script>
	</body>
</html>

<style is:global>
	html {
		font-family: system-ui, sans-serif;
		background: #13151a;
	}
	code {
		font-family:
			Menlo,
			Monaco,
			Lucida Console,
			Liberation Mono,
			DejaVu Sans Mono,
			Bitstream Vera Sans Mono,
			Courier New,
			monospace;
	}
</style>
